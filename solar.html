<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>光伏管理 - 智慧能源管理平台</title>
    
    <!-- 外部CSS库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    
    <!-- 外部JS库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        /* 光伏管理页面特定样式 */
        .solar-tabs {
            display: flex;
            background: white;
            border-radius: 8px 8px 0 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 0;
        }
        
        .tab-button {
            flex: 1;
            padding: 16px 24px;
            border: none;
            background: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #8c8c8c;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }
        
        .tab-button.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
            background: #f8f9fa;
        }
        
        .tab-content {
            background: white;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 24px;
            min-height: 600px;
        }
        
        .solar-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .solar-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #52c41a;
        }
        
        .solar-card.warning {
            border-left-color: #faad14;
        }
        
        .solar-card.error {
            border-left-color: #f5222d;
        }
        
        .solar-card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }
        
        .solar-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: #52c41a;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        
        .solar-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin: 8px 0;
        }
        
        .solar-unit {
            font-size: 14px;
            color: #8c8c8c;
        }
        
        .solar-trend {
            font-size: 12px;
            margin-top: 4px;
        }
        
        .trend-up {
            color: #52c41a;
        }
        
        .trend-down {
            color: #f5222d;
        }
        
        .monitoring-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin-bottom: 24px;
        }
        
        .chart-container {
            height: 300px;
            position: relative;
        }
        
        .control-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .control-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .control-switch {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .switch {
            position: relative;
            width: 50px;
            height: 24px;
            background: #ccc;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .switch.active {
            background: #52c41a;
        }
        
        .switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s;
        }
        
        .switch.active::after {
            transform: translateX(26px);
        }
        
        .alert-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .alert-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border-left: 4px solid #faad14;
            background: #fffbe6;
            border-radius: 4px;
            margin-bottom: 8px;
        }
        
        .alert-item.critical {
            border-left-color: #f5222d;
            background: #fff2f0;
        }
        
        .alert-item.info {
            border-left-color: #1890ff;
            background: #e6f7ff;
        }
        
        .gis-container {
            height: 500px;
            background: #f0f2f5;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        .solar-panel-grid {
            display: grid;
            grid-template-columns: repeat(10, 1fr);
            gap: 4px;
            padding: 20px;
        }
        
        .solar-panel {
            width: 40px;
            height: 20px;
            border-radius: 2px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
        }
        
        .solar-panel.high-power {
            background: #52c41a;
        }
        
        .solar-panel.medium-power {
            background: #faad14;
        }
        
        .solar-panel.low-power {
            background: #f5222d;
        }
        
        .solar-panel.offline {
            background: #d9d9d9;
        }
        
        .solar-panel:hover {
            transform: scale(1.2);
            z-index: 10;
        }
        
        .legend {
            position: absolute;
            top: 20px;
            right: 20px;
            background: white;
            padding: 16px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }
        
        .legend-color {
            width: 16px;
            height: 8px;
            border-radius: 2px;
        }
        
        /* 表格样式 */
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 16px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .table th {
            background: #fafafa;
            font-weight: 500;
            color: #333;
        }

        .table-responsive {
            overflow-x: auto;
        }

        /* 表单控件样式 */
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        /* 按钮样式增强 */
        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
        }

        .text-success {
            color: #52c41a !important;
        }

        .text-warning {
            color: #faad14 !important;
        }

        .text-danger {
            color: #f5222d !important;
        }

        /* 动画效果 */
        .solar-panel {
            transition: all 0.3s ease;
        }

        .solar-panel:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .alert-item {
            transition: all 0.3s ease;
        }

        .alert-item:hover {
            transform: translateX(4px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        /* 加载动画 */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #1890ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 状态指示器 */
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-indicator.online {
            background: #52c41a;
            animation: pulse 2s infinite;
        }

        .status-indicator.offline {
            background: #f5222d;
        }

        .status-indicator.warning {
            background: #faad14;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(82, 196, 26, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
            }
        }

        @media (max-width: 768px) {
            .monitoring-grid {
                grid-template-columns: 1fr;
            }

            .solar-overview {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }

            .control-panel {
                grid-template-columns: 1fr;
            }

            .solar-tabs {
                flex-direction: column;
            }

            .tab-button {
                border-bottom: none;
                border-right: 3px solid transparent;
            }

            .tab-button.active {
                border-right-color: #1890ff;
                border-bottom-color: transparent;
            }

            .solar-panel-grid {
                grid-template-columns: repeat(5, 1fr);
            }

            .legend {
                position: static;
                margin-top: 16px;
            }
        }

        @media (max-width: 480px) {
            .solar-overview {
                grid-template-columns: 1fr;
            }

            .solar-panel-grid {
                grid-template-columns: repeat(4, 1fr);
            }

            .tab-button {
                padding: 12px 16px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化页面布局
            initializeLayout('光伏管理', ['运营中心', '光伏管理']);
            
            // 获取页面内容容器
            const pageContent = document.getElementById('page-content');
            
            // 插入页面内容
            pageContent.innerHTML = `
                <!-- 标签页导航 -->
                <div class="solar-tabs">
                    <button class="tab-button active" onclick="switchTab('monitoring')">
                        <i class="fas fa-chart-line" style="margin-right: 8px;"></i>
                        实时监测
                    </button>
                    <button class="tab-button" onclick="switchTab('control')">
                        <i class="fas fa-sliders-h" style="margin-right: 8px;"></i>
                        远程控制
                    </button>
                    <button class="tab-button" onclick="switchTab('visualization')">
                        <i class="fas fa-map" style="margin-right: 8px;"></i>
                        可视化监控
                    </button>
                    <button class="tab-button" onclick="switchTab('alerts')">
                        <i class="fas fa-exclamation-triangle" style="margin-right: 8px;"></i>
                        告警管理
                    </button>
                </div>

                <!-- 标签页内容 -->
                <div class="tab-content">
                    <div id="monitoring-content">
                        <!-- 实时监测内容将在这里插入 -->
                    </div>
                    <div id="control-content" style="display: none;">
                        <!-- 远程控制内容将在这里插入 -->
                    </div>
                    <div id="visualization-content" style="display: none;">
                        <!-- 可视化监控内容将在这里插入 -->
                    </div>
                    <div id="alerts-content" style="display: none;">
                        <!-- 告警管理内容将在这里插入 -->
                    </div>
                </div>
            `;
            
            // 初始化光伏管理页面
            initializeSolarManagement();
        });

        // 切换标签页
        function switchTab(tabName) {
            // 更新标签按钮状态
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // 隐藏所有内容
            document.querySelectorAll('[id$="-content"]').forEach(content => {
                content.style.display = 'none';
            });

            // 显示选中的内容
            document.getElementById(tabName + '-content').style.display = 'block';

            // 根据标签页加载相应内容
            switch(tabName) {
                case 'monitoring':
                    loadMonitoringContent();
                    break;
                case 'control':
                    loadControlContent();
                    break;
                case 'visualization':
                    loadVisualizationContent();
                    break;
                case 'alerts':
                    loadAlertsContent();
                    break;
            }
        }

        // 初始化光伏管理页面
        function initializeSolarManagement() {
            loadMonitoringContent();
        }

        // 加载实时监测内容
        function loadMonitoringContent() {
            const content = document.getElementById('monitoring-content');
            content.innerHTML = `
                <!-- 光伏概览卡片 -->
                <div class="solar-overview">
                    <div class="solar-card">
                        <div class="solar-card-header">
                            <div class="solar-icon">
                                <i class="fas fa-solar-panel"></i>
                            </div>
                            <span class="status-tag status-success">正常</span>
                        </div>
                        <div class="solar-value">1,247.8 <span class="solar-unit">kWh</span></div>
                        <div class="solar-trend trend-up">
                            <i class="fas fa-arrow-up"></i> 今日发电量 +12.3%
                        </div>
                    </div>

                    <div class="solar-card">
                        <div class="solar-card-header">
                            <div class="solar-icon" style="background: #1890ff;">
                                <i class="fas fa-bolt"></i>
                            </div>
                            <span class="status-tag status-success">正常</span>
                        </div>
                        <div class="solar-value">856.2 <span class="solar-unit">kW</span></div>
                        <div class="solar-trend trend-up">
                            <i class="fas fa-arrow-up"></i> 实时功率 +5.7%
                        </div>
                    </div>

                    <div class="solar-card">
                        <div class="solar-card-header">
                            <div class="solar-icon" style="background: #faad14;">
                                <i class="fas fa-thermometer-half"></i>
                            </div>
                            <span class="status-tag status-warning">注意</span>
                        </div>
                        <div class="solar-value">68.5 <span class="solar-unit">°C</span></div>
                        <div class="solar-trend">
                            <i class="fas fa-exclamation-triangle"></i> 逆变器温度偏高
                        </div>
                    </div>

                    <div class="solar-card">
                        <div class="solar-card-header">
                            <div class="solar-icon" style="background: #722ed1;">
                                <i class="fas fa-eye"></i>
                            </div>
                            <span class="status-tag status-success">良好</span>
                        </div>
                        <div class="solar-value">92.1 <span class="solar-unit">%</span></div>
                        <div class="solar-trend trend-down">
                            <i class="fas fa-arrow-down"></i> 组件清洁度 -2.1%
                        </div>
                    </div>
                </div>

                <!-- 监测图表 -->
                <div class="monitoring-grid">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">发电功率趋势</h3>
                            <button class="btn btn-primary" onclick="refreshPowerChart()">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="powerChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">环境参数监测</h3>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="environmentChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 详细参数表格 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">光伏电站详细参数</h3>
                        <button class="btn btn-primary" onclick="exportData()">
                            <i class="fas fa-download"></i>
                            导出数据
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>参数名称</th>
                                        <th>当前值</th>
                                        <th>单位</th>
                                        <th>状态</th>
                                        <th>更新时间</th>
                                    </tr>
                                </thead>
                                <tbody id="parametersTable">
                                    <!-- 参数数据将通过JavaScript动态插入 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;

            // 初始化图表
            initializePowerChart();
            initializeEnvironmentChart();
            loadParametersData();
        }

        // 加载远程控制内容
        function loadControlContent() {
            const content = document.getElementById('control-content');
            content.innerHTML = `
                <div class="control-panel">
                    <div class="control-card">
                        <h3 style="margin-bottom: 20px;">
                            <i class="fas fa-power-off" style="margin-right: 8px;"></i>
                            系统控制
                        </h3>
                        <div class="control-switch">
                            <span>主系统开关</span>
                            <div class="switch active" onclick="toggleSwitch(this, 'main-system')"></div>
                        </div>
                        <div class="control-switch">
                            <span>逆变器1</span>
                            <div class="switch active" onclick="toggleSwitch(this, 'inverter-1')"></div>
                        </div>
                        <div class="control-switch">
                            <span>逆变器2</span>
                            <div class="switch active" onclick="toggleSwitch(this, 'inverter-2')"></div>
                        </div>
                        <div class="control-switch">
                            <span>逆变器3</span>
                            <div class="switch" onclick="toggleSwitch(this, 'inverter-3')"></div>
                        </div>
                    </div>

                    <div class="control-card">
                        <h3 style="margin-bottom: 20px;">
                            <i class="fas fa-cog" style="margin-right: 8px;"></i>
                            运行模式
                        </h3>
                        <div style="margin-bottom: 16px;">
                            <label style="display: block; margin-bottom: 8px;">运行模式选择：</label>
                            <select class="form-control" onchange="changeOperationMode(this.value)">
                                <option value="auto">自动模式</option>
                                <option value="manual">手动模式</option>
                                <option value="maintenance">维护模式</option>
                                <option value="emergency">应急模式</option>
                            </select>
                        </div>
                        <div style="margin-bottom: 16px;">
                            <label style="display: block; margin-bottom: 8px;">功率限制 (%)：</label>
                            <input type="range" min="0" max="100" value="100" class="form-control"
                                   onchange="setPowerLimit(this.value)" style="margin-bottom: 8px;">
                            <span id="powerLimitValue">100%</span>
                        </div>
                        <button class="btn btn-primary" onclick="applySettings()">
                            <i class="fas fa-check"></i>
                            应用设置
                        </button>
                    </div>

                    <div class="control-card">
                        <h3 style="margin-bottom: 20px;">
                            <i class="fas fa-tools" style="margin-right: 8px;"></i>
                            故障诊断
                        </h3>
                        <button class="btn btn-primary" onclick="runDiagnostic()" style="margin-bottom: 12px; width: 100%;">
                            <i class="fas fa-search"></i>
                            运行系统诊断
                        </button>
                        <button class="btn btn-warning" onclick="resetSystem()" style="margin-bottom: 12px; width: 100%;">
                            <i class="fas fa-redo"></i>
                            重启系统
                        </button>
                        <button class="btn btn-danger" onclick="emergencyStop()" style="width: 100%;">
                            <i class="fas fa-stop"></i>
                            紧急停机
                        </button>

                        <div id="diagnosticResult" style="margin-top: 16px; padding: 12px; background: #f8f9fa; border-radius: 4px; display: none;">
                            <!-- 诊断结果将在这里显示 -->
                        </div>
                    </div>
                </div>

                <!-- 控制日志 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">控制操作日志</h3>
                        <button class="btn btn-primary" onclick="refreshControlLog()">
                            <i class="fas fa-sync-alt"></i>
                            刷新
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>时间</th>
                                        <th>操作类型</th>
                                        <th>设备</th>
                                        <th>操作内容</th>
                                        <th>操作员</th>
                                        <th>结果</th>
                                    </tr>
                                </thead>
                                <tbody id="controlLogTable">
                                    <!-- 控制日志数据将通过JavaScript动态插入 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;

            loadControlLog();
        }

        // 加载可视化监控内容
        function loadVisualizationContent() {
            const content = document.getElementById('visualization-content');
            content.innerHTML = `
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-map" style="margin-right: 8px;"></i>
                            光伏电站可视化监控
                        </h3>
                        <div>
                            <button class="btn btn-primary" onclick="refreshVisualization()">
                                <i class="fas fa-sync-alt"></i>
                                刷新
                            </button>
                            <button class="btn btn-primary" onclick="toggleFullscreen()">
                                <i class="fas fa-expand"></i>
                                全屏
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="gis-container" id="gisContainer">
                            <div class="solar-panel-grid" id="solarPanelGrid">
                                <!-- 光伏组件将通过JavaScript动态生成 -->
                            </div>

                            <div class="legend">
                                <h4 style="margin-bottom: 12px; font-size: 14px;">功率密度图例</h4>
                                <div class="legend-item">
                                    <div class="legend-color high-power"></div>
                                    <span>高功率 (>80%)</span>
                                </div>
                                <div class="legend-item">
                                    <div class="legend-color medium-power"></div>
                                    <span>中功率 (50-80%)</span>
                                </div>
                                <div class="legend-item">
                                    <div class="legend-color low-power"></div>
                                    <span>低功率 (<50%)</span>
                                </div>
                                <div class="legend-item">
                                    <div class="legend-color offline"></div>
                                    <span>离线</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 组件详情面板 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">组件详细信息</h3>
                    </div>
                    <div class="card-body">
                        <div id="componentDetails">
                            <p style="text-align: center; color: #8c8c8c; margin: 40px 0;">
                                点击上方光伏组件查看详细信息
                            </p>
                        </div>
                    </div>
                </div>
            `;

            generateSolarPanels();
        }

        // 加载告警管理内容
        function loadAlertsContent() {
            const content = document.getElementById('alerts-content');
            content.innerHTML = `
                <!-- 告警统计 -->
                <div class="solar-overview">
                    <div class="solar-card error">
                        <div class="solar-card-header">
                            <div class="solar-icon" style="background: #f5222d;">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <span class="status-tag status-danger">紧急</span>
                        </div>
                        <div class="solar-value">2 <span class="solar-unit">个</span></div>
                        <div>紧急告警</div>
                    </div>

                    <div class="solar-card warning">
                        <div class="solar-card-header">
                            <div class="solar-icon" style="background: #faad14;">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <span class="status-tag status-warning">重要</span>
                        </div>
                        <div class="solar-value">5 <span class="solar-unit">个</span></div>
                        <div>重要告警</div>
                    </div>

                    <div class="solar-card">
                        <div class="solar-card-header">
                            <div class="solar-icon" style="background: #1890ff;">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <span class="status-tag status-info">提示</span>
                        </div>
                        <div class="solar-value">12 <span class="solar-unit">个</span></div>
                        <div>提示告警</div>
                    </div>

                    <div class="solar-card">
                        <div class="solar-card-header">
                            <div class="solar-icon" style="background: #52c41a;">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <span class="status-tag status-success">正常</span>
                        </div>
                        <div class="solar-value">98.2 <span class="solar-unit">%</span></div>
                        <div>系统健康度</div>
                    </div>
                </div>

                <!-- 告警列表 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">实时告警列表</h3>
                        <div>
                            <button class="btn btn-primary" onclick="refreshAlerts()">
                                <i class="fas fa-sync-alt"></i>
                                刷新
                            </button>
                            <button class="btn btn-warning" onclick="acknowledgeAll()">
                                <i class="fas fa-check"></i>
                                全部确认
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="alert-list" id="alertsList">
                            <!-- 告警列表将通过JavaScript动态插入 -->
                        </div>
                    </div>
                </div>

                <!-- 告警配置 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">告警配置</h3>
                    </div>
                    <div class="card-body">
                        <div class="control-panel">
                            <div class="control-card">
                                <h4>温度告警阈值</h4>
                                <div style="margin-bottom: 12px;">
                                    <label>逆变器过温阈值 (°C)：</label>
                                    <input type="number" value="85" class="form-control" id="tempThreshold">
                                </div>
                                <div style="margin-bottom: 12px;">
                                    <label>组件温度阈值 (°C)：</label>
                                    <input type="number" value="75" class="form-control" id="panelTempThreshold">
                                </div>
                            </div>

                            <div class="control-card">
                                <h4>功率告警阈值</h4>
                                <div style="margin-bottom: 12px;">
                                    <label>功率下降阈值 (%)：</label>
                                    <input type="number" value="5" class="form-control" id="powerDropThreshold">
                                </div>
                                <div style="margin-bottom: 12px;">
                                    <label>效率下降阈值 (%)：</label>
                                    <input type="number" value="10" class="form-control" id="efficiencyThreshold">
                                </div>
                            </div>

                            <div class="control-card">
                                <h4>通知设置</h4>
                                <div class="control-switch">
                                    <span>企业微信推送</span>
                                    <div class="switch active" onclick="toggleSwitch(this, 'wechat-notify')"></div>
                                </div>
                                <div class="control-switch">
                                    <span>APP推送</span>
                                    <div class="switch active" onclick="toggleSwitch(this, 'app-notify')"></div>
                                </div>
                                <div class="control-switch">
                                    <span>声光报警</span>
                                    <div class="switch active" onclick="toggleSwitch(this, 'audio-notify')"></div>
                                </div>
                                <button class="btn btn-primary" onclick="saveAlertConfig()" style="margin-top: 16px; width: 100%;">
                                    <i class="fas fa-save"></i>
                                    保存配置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            loadAlertsList();
        }

        // 初始化功率图表
        function initializePowerChart() {
            const ctx = document.getElementById('powerChart');
            if (!ctx) return;

            const data = {
                labels: Array.from({length: 24}, (_, i) => `${i.toString().padStart(2, '0')}:00`),
                datasets: [{
                    label: '发电功率 (kW)',
                    data: generatePowerData(),
                    borderColor: '#52c41a',
                    backgroundColor: 'rgba(82, 196, 26, 0.1)',
                    fill: true,
                    tension: 0.4
                }]
            };

            window.powerChart = createLineChart('powerChart', data);
        }

        // 初始化环境图表
        function initializeEnvironmentChart() {
            const ctx = document.getElementById('environmentChart');
            if (!ctx) return;

            const data = {
                labels: ['温度', '湿度', '光照强度', '风速', '清洁度'],
                datasets: [{
                    label: '环境参数',
                    data: [68.5, 45.2, 850, 3.2, 92.1],
                    backgroundColor: [
                        '#f5222d',
                        '#1890ff',
                        '#faad14',
                        '#52c41a',
                        '#722ed1'
                    ]
                }]
            };

            createPolarChart('environmentChart', data);
        }

        // 生成功率数据
        function generatePowerData() {
            const data = [];
            for (let i = 0; i < 24; i++) {
                if (i < 6 || i > 18) {
                    data.push(0); // 夜间无发电
                } else {
                    // 白天发电，中午最高
                    const peak = 12;
                    const distance = Math.abs(i - peak);
                    const maxPower = 1000;
                    const power = maxPower * Math.exp(-distance * distance / 20) + Math.random() * 50;
                    data.push(Math.max(0, power));
                }
            }
            return data;
        }

        // 加载参数数据
        function loadParametersData() {
            const tbody = document.getElementById('parametersTable');
            if (!tbody) return;

            const parameters = [
                { name: '总发电功率', value: '856.2', unit: 'kW', status: '正常', time: '2024-01-15 14:30:25' },
                { name: '逆变器1功率', value: '285.4', unit: 'kW', status: '正常', time: '2024-01-15 14:30:25' },
                { name: '逆变器2功率', value: '290.1', unit: 'kW', status: '正常', time: '2024-01-15 14:30:25' },
                { name: '逆变器3功率', value: '280.7', unit: 'kW', status: '正常', time: '2024-01-15 14:30:25' },
                { name: '直流电压', value: '650.5', unit: 'V', status: '正常', time: '2024-01-15 14:30:25' },
                { name: '直流电流', value: '1315.8', unit: 'A', status: '正常', time: '2024-01-15 14:30:25' },
                { name: '逆变器温度', value: '68.5', unit: '°C', status: '警告', time: '2024-01-15 14:30:25' },
                { name: '组件温度', value: '45.2', unit: '°C', status: '正常', time: '2024-01-15 14:30:25' },
                { name: '光照强度', value: '850', unit: 'W/m²', status: '正常', time: '2024-01-15 14:30:25' },
                { name: '组件清洁度', value: '92.1', unit: '%', status: '良好', time: '2024-01-15 14:30:25' }
            ];

            tbody.innerHTML = parameters.map(param => `
                <tr>
                    <td>${param.name}</td>
                    <td>${param.value}</td>
                    <td>${param.unit}</td>
                    <td>
                        <span class="status-tag ${getStatusClass(param.status)}">${param.status}</span>
                    </td>
                    <td>${param.time}</td>
                </tr>
            `).join('');
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch(status) {
                case '正常': return 'status-success';
                case '良好': return 'status-success';
                case '警告': return 'status-warning';
                case '异常': return 'status-danger';
                default: return 'status-info';
            }
        }

        // 切换开关
        function toggleSwitch(element, deviceId) {
            element.classList.toggle('active');
            const isActive = element.classList.contains('active');

            // 模拟设备控制
            console.log(`设备 ${deviceId} ${isActive ? '开启' : '关闭'}`);
            showNotification(`设备 ${deviceId} 已${isActive ? '开启' : '关闭'}`, 'success');
        }

        // 生成光伏组件
        function generateSolarPanels() {
            const grid = document.getElementById('solarPanelGrid');
            if (!grid) return;

            grid.innerHTML = '';

            for (let i = 0; i < 100; i++) {
                const panel = document.createElement('div');
                panel.className = 'solar-panel';
                panel.dataset.panelId = i + 1;

                // 随机生成功率状态
                const powerLevel = Math.random();
                if (powerLevel > 0.8) {
                    panel.classList.add('high-power');
                } else if (powerLevel > 0.5) {
                    panel.classList.add('medium-power');
                } else if (powerLevel > 0.1) {
                    panel.classList.add('low-power');
                } else {
                    panel.classList.add('offline');
                }

                panel.onclick = () => showPanelDetails(i + 1, powerLevel);
                grid.appendChild(panel);
            }
        }

        // 显示组件详情
        function showPanelDetails(panelId, powerLevel) {
            const details = document.getElementById('componentDetails');
            if (!details) return;

            const power = (powerLevel * 100).toFixed(1);
            const voltage = (24 + Math.random() * 2).toFixed(1);
            const current = (powerLevel * 10 + Math.random()).toFixed(2);
            const temp = (25 + Math.random() * 20).toFixed(1);

            details.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                    <div>
                        <h4>组件 #${panelId}</h4>
                        <p><strong>功率输出:</strong> ${power}%</p>
                        <p><strong>电压:</strong> ${voltage} V</p>
                        <p><strong>电流:</strong> ${current} A</p>
                    </div>
                    <div>
                        <p><strong>温度:</strong> ${temp} °C</p>
                        <p><strong>状态:</strong> ${powerLevel > 0.1 ? '正常' : '离线'}</p>
                        <p><strong>清洁度:</strong> ${(85 + Math.random() * 15).toFixed(1)}%</p>
                    </div>
                </div>
            `;
        }

        // 加载告警列表
        function loadAlertsList() {
            const alertsList = document.getElementById('alertsList');
            if (!alertsList) return;

            const alerts = [
                {
                    type: 'critical',
                    icon: 'fas fa-exclamation-circle',
                    title: '逆变器2温度过高',
                    description: '逆变器2温度达到87°C，超过安全阈值85°C',
                    time: '2分钟前',
                    location: '逆变器房-2号机'
                },
                {
                    type: 'critical',
                    icon: 'fas fa-exclamation-circle',
                    title: '组件串并联失配',
                    description: '第3排组件串联电压异常，可能存在组件故障',
                    time: '5分钟前',
                    location: '光伏阵列-第3排'
                },
                {
                    type: 'warning',
                    icon: 'fas fa-exclamation-triangle',
                    title: '发电效率下降',
                    description: '组件清洁度下降导致发电效率降低6.2%',
                    time: '15分钟前',
                    location: '光伏阵列-东区'
                },
                {
                    type: 'warning',
                    icon: 'fas fa-exclamation-triangle',
                    title: '逆变器1通信异常',
                    description: '逆变器1通信间歇性中断，数据传输不稳定',
                    time: '28分钟前',
                    location: '逆变器房-1号机'
                },
                {
                    type: 'info',
                    icon: 'fas fa-info-circle',
                    title: '定期维护提醒',
                    description: '光伏组件清洁维护计划提醒',
                    time: '1小时前',
                    location: '全站'
                }
            ];

            alertsList.innerHTML = alerts.map(alert => `
                <div class="alert-item ${alert.type}">
                    <i class="${alert.icon}"></i>
                    <div style="flex: 1;">
                        <div style="font-weight: 500; margin-bottom: 4px;">${alert.title}</div>
                        <div style="font-size: 12px; color: #8c8c8c; margin-bottom: 4px;">${alert.description}</div>
                        <div style="font-size: 11px; color: #8c8c8c;">
                            <i class="fas fa-map-marker-alt"></i> ${alert.location} • ${alert.time}
                        </div>
                    </div>
                    <button class="btn btn-sm" onclick="acknowledgeAlert(this)">
                        <i class="fas fa-check"></i>
                    </button>
                </div>
            `).join('');
        }

        // 加载控制日志
        function loadControlLog() {
            const tbody = document.getElementById('controlLogTable');
            if (!tbody) return;

            const logs = [
                { time: '2024-01-15 14:25:30', type: '系统控制', device: '主系统', operation: '启动系统', operator: '张工', result: '成功' },
                { time: '2024-01-15 14:20:15', type: '设备控制', device: '逆变器3', operation: '关闭设备', operator: '李工', result: '成功' },
                { time: '2024-01-15 14:15:45', type: '参数调整', device: '功率限制', operation: '调整至90%', operator: '王工', result: '成功' },
                { time: '2024-01-15 14:10:20', type: '故障诊断', device: '全系统', operation: '运行诊断', operator: '系统', result: '完成' },
                { time: '2024-01-15 14:05:10', type: '模式切换', device: '运行模式', operation: '切换至自动模式', operator: '张工', result: '成功' }
            ];

            tbody.innerHTML = logs.map(log => `
                <tr>
                    <td>${log.time}</td>
                    <td>${log.type}</td>
                    <td>${log.device}</td>
                    <td>${log.operation}</td>
                    <td>${log.operator}</td>
                    <td>
                        <span class="status-tag status-success">${log.result}</span>
                    </td>
                </tr>
            `).join('');
        }

        // 刷新功率图表
        function refreshPowerChart() {
            if (window.powerChart) {
                const newData = {
                    labels: Array.from({length: 24}, (_, i) => `${i.toString().padStart(2, '0')}:00`),
                    datasets: [{
                        label: '发电功率 (kW)',
                        data: generatePowerData(),
                        borderColor: '#52c41a',
                        backgroundColor: 'rgba(82, 196, 26, 0.1)',
                        fill: true,
                        tension: 0.4
                    }]
                };
                updateChartData(window.powerChart, newData);
                showNotification('功率图表已更新', 'success');
            }
        }

        // 其他功能函数
        function exportData() {
            showNotification('数据导出功能开发中...', 'info');
        }

        function changeOperationMode(mode) {
            showNotification(`运行模式已切换至: ${mode}`, 'success');
        }

        function setPowerLimit(value) {
            document.getElementById('powerLimitValue').textContent = value + '%';
        }

        function applySettings() {
            showNotification('设置已应用', 'success');
        }

        function runDiagnostic() {
            const result = document.getElementById('diagnosticResult');
            result.style.display = 'block';
            result.innerHTML = `
                <h5>系统诊断结果</h5>
                <p><i class="fas fa-check text-success"></i> 主系统运行正常</p>
                <p><i class="fas fa-check text-success"></i> 逆变器1-2运行正常</p>
                <p><i class="fas fa-exclamation-triangle text-warning"></i> 逆变器3离线</p>
                <p><i class="fas fa-exclamation-triangle text-warning"></i> 第3排组件电压异常</p>
            `;
            showNotification('系统诊断完成', 'success');
        }

        function resetSystem() {
            if (confirm('确定要重启系统吗？')) {
                showNotification('系统重启中...', 'warning');
            }
        }

        function emergencyStop() {
            if (confirm('确定要执行紧急停机吗？')) {
                showNotification('紧急停机已执行', 'danger');
            }
        }

        function refreshVisualization() {
            generateSolarPanels();
            showNotification('可视化数据已刷新', 'success');
        }

        function toggleFullscreen() {
            const container = document.getElementById('gisContainer');
            if (container.requestFullscreen) {
                container.requestFullscreen();
            }
        }

        function refreshAlerts() {
            loadAlertsList();
            showNotification('告警列表已刷新', 'success');
        }

        function acknowledgeAlert(button) {
            const alertItem = button.closest('.alert-item');
            alertItem.style.opacity = '0.5';
            button.innerHTML = '<i class="fas fa-check"></i> 已确认';
            button.disabled = true;
        }

        function acknowledgeAll() {
            document.querySelectorAll('.alert-item button').forEach(button => {
                acknowledgeAlert(button);
            });
            showNotification('所有告警已确认', 'success');
        }

        function saveAlertConfig() {
            showNotification('告警配置已保存', 'success');
        }

        function refreshControlLog() {
            loadControlLog();
            showNotification('控制日志已刷新', 'success');
        }

        // 启动实时数据更新
        function startRealTimeUpdate() {
            setInterval(() => {
                // 更新实时监测数据
                if (document.getElementById('monitoring-content').style.display !== 'none') {
                    updateRealTimeData();
                }

                // 更新可视化面板
                if (document.getElementById('visualization-content').style.display !== 'none') {
                    updateVisualizationData();
                }
            }, 30000); // 每30秒更新一次
        }

        // 更新实时数据
        function updateRealTimeData() {
            // 更新概览卡片数据
            const cards = document.querySelectorAll('.solar-card');
            cards.forEach((card, index) => {
                const valueElement = card.querySelector('.solar-value');
                if (valueElement) {
                    const currentValue = parseFloat(valueElement.textContent);
                    const variation = (Math.random() - 0.5) * 0.1; // ±5%变化
                    const newValue = currentValue * (1 + variation);

                    // 更新数值，保持原有单位
                    const unit = valueElement.querySelector('.solar-unit').textContent;
                    valueElement.innerHTML = `${newValue.toFixed(1)} <span class="solar-unit">${unit}</span>`;
                }
            });

            // 更新参数表格
            loadParametersData();
        }

        // 更新可视化数据
        function updateVisualizationData() {
            const panels = document.querySelectorAll('.solar-panel');
            panels.forEach(panel => {
                // 随机更新面板状态
                if (Math.random() < 0.1) { // 10%概率更新状态
                    const powerLevel = Math.random();
                    panel.className = 'solar-panel';

                    if (powerLevel > 0.8) {
                        panel.classList.add('high-power');
                    } else if (powerLevel > 0.5) {
                        panel.classList.add('medium-power');
                    } else if (powerLevel > 0.1) {
                        panel.classList.add('low-power');
                    } else {
                        panel.classList.add('offline');
                    }
                }
            });
        }

        // 页面加载完成后启动实时更新
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                startRealTimeUpdate();
            }, 5000); // 5秒后开始实时更新
        });

        // 添加键盘快捷键支持
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case '1':
                        e.preventDefault();
                        switchTab('monitoring');
                        break;
                    case '2':
                        e.preventDefault();
                        switchTab('control');
                        break;
                    case '3':
                        e.preventDefault();
                        switchTab('visualization');
                        break;
                    case '4':
                        e.preventDefault();
                        switchTab('alerts');
                        break;
                    case 'r':
                        e.preventDefault();
                        location.reload();
                        break;
                }
            }
        });

        // 添加页面可见性检测，页面不可见时暂停更新
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                // 页面隐藏时可以暂停一些更新
                console.log('页面隐藏，暂停部分更新');
            } else {
                // 页面重新可见时恢复更新
                console.log('页面可见，恢复更新');
                updateRealTimeData();
            }
        });
    </script>

    <!-- 引入公共脚本 -->
    <script src="assets/js/common.js"></script>
    <script src="assets/js/charts.js"></script>
</body>
</html>
