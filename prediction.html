<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI预测 - 智慧能源管理平台</title>
    
    <!-- 外部CSS库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    
    <!-- 外部JS库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        .prediction-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 24px;
            margin-bottom: 24px;
        }
        
        .model-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .model-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
            position: relative;
        }
        
        .model-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: #1890ff;
            border-radius: 8px 8px 0 0;
        }
        
        .model-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: linear-gradient(135deg, #1890ff, #40a9ff);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            margin: 0 auto 16px;
        }
        
        .model-accuracy {
            font-size: 24px;
            font-weight: bold;
            color: #52c41a;
            margin: 8px 0;
        }
        
        .prediction-controls {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 24px;
        }
        
        .control-row {
            display: flex;
            align-items: center;
            gap: 16px;
            flex-wrap: wrap;
        }
        
        .recommendation-list {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .recommendation-item {
            display: flex;
            align-items: flex-start;
            gap: 16px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 12px;
            border-left: 4px solid #1890ff;
        }
        
        .recommendation-item.high-priority {
            border-left-color: #f5222d;
            background: #fff2f0;
        }
        
        .recommendation-item.medium-priority {
            border-left-color: #faad14;
            background: #fffbe6;
        }
        
        .recommendation-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #1890ff;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            flex-shrink: 0;
        }
        
        .recommendation-content {
            flex: 1;
        }
        
        .recommendation-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }
        
        .recommendation-desc {
            font-size: 14px;
            color: #8c8c8c;
            margin-bottom: 8px;
        }
        
        .recommendation-impact {
            font-size: 12px;
            color: #52c41a;
            font-weight: 500;
        }
        
        .forecast-table {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .forecast-row {
            display: grid;
            grid-template-columns: 1fr repeat(4, 80px);
            align-items: center;
            padding: 12px 20px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .forecast-row.header {
            background: #fafafa;
            font-weight: 600;
        }
        
        .confidence-bar {
            width: 100%;
            height: 6px;
            background: #f0f0f0;
            border-radius: 3px;
            overflow: hidden;
        }
        
        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #f5222d, #faad14, #52c41a);
            border-radius: 3px;
            transition: width 0.3s;
        }
        
        .ai-insights {
            background: linear-gradient(135deg, #1890ff, #40a9ff);
            color: white;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 24px;
        }
        
        .insight-item {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }
        
        .insight-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化页面布局
            initializeLayout('AI预测', ['分析中心', 'AI预测']);
            
            // 获取页面内容容器
            const pageContent = document.getElementById('page-content');
            
            // 插入页面内容
            pageContent.innerHTML = `
                <!-- AI模型状态 -->
                <div class="model-status">
                    <div class="model-card">
                        <div class="model-icon">
                            <i class="fas fa-brain"></i>
                        </div>
                        <h4 style="margin: 0 0 8px 0; font-size: 14px;">负荷预测模型</h4>
                        <div class="model-accuracy">95.2%</div>
                        <div style="font-size: 12px; color: #8c8c8c;">预测准确率</div>
                    </div>
                    
                    <div class="model-card">
                        <div class="model-icon" style="background: linear-gradient(135deg, #52c41a, #73d13d);">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h4 style="margin: 0 0 8px 0; font-size: 14px;">趋势预测模型</h4>
                        <div class="model-accuracy">92.8%</div>
                        <div style="font-size: 12px; color: #8c8c8c;">预测准确率</div>
                    </div>
                    
                    <div class="model-card">
                        <div class="model-icon" style="background: linear-gradient(135deg, #faad14, #ffc53d);">
                            <i class="fas fa-lightbulb"></i>
                        </div>
                        <h4 style="margin: 0 0 8px 0; font-size: 14px;">优化建议模型</h4>
                        <div class="model-accuracy">88.5%</div>
                        <div style="font-size: 12px; color: #8c8c8c;">建议采纳率</div>
                    </div>
                    
                    <div class="model-card">
                        <div class="model-icon" style="background: linear-gradient(135deg, #722ed1, #9254de);">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <h4 style="margin: 0 0 8px 0; font-size: 14px;">异常检测模型</h4>
                        <div class="model-accuracy">96.7%</div>
                        <div style="font-size: 12px; color: #8c8c8c;">检测准确率</div>
                    </div>
                </div>

                <!-- AI洞察 -->
                <div class="ai-insights">
                    <h3 style="margin: 0 0 20px 0; font-size: 18px; font-weight: 600;">
                        <i class="fas fa-robot" style="margin-right: 8px;"></i>
                        AI智能洞察
                    </h3>
                    <div class="insight-item">
                        <div class="insight-icon">
                            <i class="fas fa-arrow-up"></i>
                        </div>
                        <div>
                            <strong>负荷预测：</strong>明日用电高峰预计在14:00-16:00，峰值负荷约1,850kW，建议提前启动错峰策略
                        </div>
                    </div>
                    <div class="insight-item">
                        <div class="insight-icon">
                            <i class="fas fa-leaf"></i>
                        </div>
                        <div>
                            <strong>节能机会：</strong>检测到空调系统在非工作时间仍保持高功率运行，优化后可节能约15%
                        </div>
                    </div>
                    <div class="insight-item">
                        <div class="insight-icon">
                            <i class="fas fa-exclamation"></i>
                        </div>
                        <div>
                            <strong>异常预警：</strong>电梯系统功耗模式异常，建议安排维护检查，预防潜在故障
                        </div>
                    </div>
                </div>

                <!-- 预测控制面板 -->
                <div class="prediction-controls">
                    <div class="control-row">
                        <div class="control-group">
                            <label class="control-label">预测类型</label>
                            <select class="control-input" id="predictionType" onchange="updatePrediction()">
                                <option value="load">负荷预测</option>
                                <option value="consumption">能耗预测</option>
                                <option value="cost">成本预测</option>
                                <option value="efficiency">能效预测</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <label class="control-label">预测周期</label>
                            <select class="control-input" id="predictionPeriod" onchange="updatePrediction()">
                                <option value="24h">未来24小时</option>
                                <option value="7d" selected>未来7天</option>
                                <option value="30d">未来30天</option>
                                <option value="90d">未来90天</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <label class="control-label">预测对象</label>
                            <select class="control-input" id="predictionTarget" onchange="updatePrediction()">
                                <option value="total">总体</option>
                                <option value="building">按建筑</option>
                                <option value="system">按系统</option>
                                <option value="device">按设备</option>
                            </select>
                        </div>
                        <button class="btn btn-primary" onclick="runPrediction()">
                            <i class="fas fa-play"></i>
                            运行预测
                        </button>
                    </div>
                </div>

                <!-- 预测结果 -->
                <div class="prediction-grid">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">负荷预测曲线</h3>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <div style="width: 12px; height: 12px; background: #1890ff; border-radius: 2px;"></div>
                                <span style="font-size: 12px; color: #8c8c8c;">预测值</span>
                                <div style="width: 12px; height: 12px; background: #52c41a; border-radius: 2px; margin-left: 12px;"></div>
                                <span style="font-size: 12px; color: #8c8c8c;">历史值</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div style="height: 300px; position: relative;">
                                <canvas id="predictionChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">预测准确度</h3>
                        </div>
                        <div class="card-body">
                            <div class="forecast-table">
                                <div class="forecast-row header">
                                    <div>时间段</div>
                                    <div>预测值</div>
                                    <div>实际值</div>
                                    <div>误差</div>
                                    <div>置信度</div>
                                </div>
                                <div class="forecast-row">
                                    <div>今日 14:00</div>
                                    <div>1,850</div>
                                    <div>1,823</div>
                                    <div style="color: #52c41a;">-1.5%</div>
                                    <div>
                                        <div class="confidence-bar">
                                            <div class="confidence-fill" style="width: 95%;"></div>
                                        </div>
                                        <div style="font-size: 10px; text-align: center; margin-top: 2px;">95%</div>
                                    </div>
                                </div>
                                <div class="forecast-row">
                                    <div>明日 09:00</div>
                                    <div>1,620</div>
                                    <div>-</div>
                                    <div>-</div>
                                    <div>
                                        <div class="confidence-bar">
                                            <div class="confidence-fill" style="width: 92%;"></div>
                                        </div>
                                        <div style="font-size: 10px; text-align: center; margin-top: 2px;">92%</div>
                                    </div>
                                </div>
                                <div class="forecast-row">
                                    <div>明日 14:00</div>
                                    <div>1,890</div>
                                    <div>-</div>
                                    <div>-</div>
                                    <div>
                                        <div class="confidence-bar">
                                            <div class="confidence-fill" style="width: 88%;"></div>
                                        </div>
                                        <div style="font-size: 10px; text-align: center; margin-top: 2px;">88%</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AI优化建议 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-magic" style="margin-right: 8px;"></i>
                            AI优化建议
                        </h3>
                        <button class="btn btn-primary" onclick="generateRecommendations()">
                            <i class="fas fa-sync-alt"></i>
                            刷新建议
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="recommendation-list">
                            <div class="recommendation-item high-priority">
                                <div class="recommendation-icon" style="background: #f5222d;">
                                    <i class="fas fa-exclamation"></i>
                                </div>
                                <div class="recommendation-content">
                                    <div class="recommendation-title">紧急：优化电梯运行策略</div>
                                    <div class="recommendation-desc">
                                        AI检测到电梯系统在低峰时段仍保持高频运行，建议调整运行策略
                                    </div>
                                    <div class="recommendation-impact">预计节能: 12% | 成本节约: ¥2,400/月</div>
                                </div>
                            </div>
                            
                            <div class="recommendation-item medium-priority">
                                <div class="recommendation-icon" style="background: #faad14;">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="recommendation-content">
                                    <div class="recommendation-title">调整空调运行时间表</div>
                                    <div class="recommendation-desc">
                                        基于人员活动模式分析，建议将空调启动时间推迟30分钟
                                    </div>
                                    <div class="recommendation-impact">预计节能: 8% | 成本节约: ¥1,800/月</div>
                                </div>
                            </div>
                            
                            <div class="recommendation-item">
                                <div class="recommendation-icon">
                                    <i class="fas fa-lightbulb"></i>
                                </div>
                                <div class="recommendation-content">
                                    <div class="recommendation-title">实施智能照明控制</div>
                                    <div class="recommendation-desc">
                                        在自然光充足的区域减少人工照明强度，提高能源利用效率
                                    </div>
                                    <div class="recommendation-impact">预计节能: 15% | 成本节约: ¥3,200/月</div>
                                </div>
                            </div>
                            
                            <div class="recommendation-item">
                                <div class="recommendation-icon" style="background: #52c41a;">
                                    <i class="fas fa-leaf"></i>
                                </div>
                                <div class="recommendation-content">
                                    <div class="recommendation-title">启用深度节能模式</div>
                                    <div class="recommendation-desc">
                                        在非工作时间启用深度节能模式，自动降低非关键设备功率
                                    </div>
                                    <div class="recommendation-impact">预计节能: 20% | 成本节约: ¥4,500/月</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // 初始化预测页面
            initializePrediction();
        });
        
        // 初始化预测页面
        function initializePrediction() {
            // 创建预测图表
            const predictionData = {
                labels: ['今日', '明日', '后天', '第4天', '第5天', '第6天', '第7天'],
                datasets: [{
                    label: '预测负荷 (kW)',
                    data: [1650, 1720, 1580, 1690, 1750, 1420, 1380],
                    borderColor: '#1890ff',
                    backgroundColor: 'rgba(24, 144, 255, 0.1)',
                    fill: true,
                    tension: 0.4
                }, {
                    label: '历史负荷 (kW)',
                    data: [1623, 1698, null, null, null, null, null],
                    borderColor: '#52c41a',
                    backgroundColor: 'rgba(82, 196, 26, 0.1)',
                    fill: false,
                    borderDash: [5, 5],
                    tension: 0.4
                }]
            };
            
            createLineChart('predictionChart', predictionData, {
                plugins: {
                    legend: {
                        display: false
                    }
                }
            });
        }
        
        // 更新预测
        function updatePrediction() {
            const type = document.getElementById('predictionType').value;
            const period = document.getElementById('predictionPeriod').value;
            const target = document.getElementById('predictionTarget').value;
            
            showNotification(`正在更新${type}预测 (${period})`, 'info');
        }
        
        // 运行预测
        function runPrediction() {
            showNotification('AI模型正在运行预测分析...', 'info');
            
            setTimeout(() => {
                showNotification('预测分析完成', 'success');
            }, 2000);
        }
        
        // 生成建议
        function generateRecommendations() {
            showNotification('AI正在生成优化建议...', 'info');
            
            setTimeout(() => {
                showNotification('优化建议已更新', 'success');
            }, 1500);
        }
    </script>
    
    <!-- 引入公共脚本 -->
    <script src="assets/js/common.js"></script>
    <script src="assets/js/charts.js"></script>
</body>
</html>
