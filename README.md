# 智慧能源管理平台 - 前端原型

## 项目概述

这是一个完整的智慧能源管理平台前端原型，包含了现代化的用户界面设计和丰富的功能模块。系统采用响应式设计，支持多种设备访问。

## 功能特性

### 🏠 首页总览
- 实时能耗数据展示
- 关键指标仪表盘
- 设备状态监控
- 最新告警信息

### 📊 监测中心
- **实时监测**: 多能源数据采集、实时监控仪表
- **数据可视化**: 能耗一张图、GIS地图、设备状态展示

### 📈 分析中心
- **用能分析**: 趋势分析、对标分析、节能潜力识别
- **AI预测**: 负荷预测、趋势预测、智能优化建议

### 🔧 运营中心
- **告警管理**: 智能告警、异常预警、故障分析
- **控制策略**: 智能调度、策略执行、能效评估
- **设备管理**: 设备台账、工单管理、巡检记录

### 📋 报表中心
- 自定义报表生成
- 多格式数据导出
- 决策支持分析

### 🌱 碳资产管理
- 碳排放测算
- 碳足迹分析
- 碳交易支持

### ⚙️ 系统管理
- 用户权限管理
- 系统配置
- 日志审计

## 技术栈

- **前端框架**: 原生HTML5 + CSS3 + JavaScript
- **UI组件**: Font Awesome 图标库
- **图表库**: Chart.js
- **地图库**: Leaflet
- **设计风格**: 现代化扁平设计，蓝绿色主题

## 项目结构

```
智慧能源管理平台/
├── index.html              # 主页面
├── monitoring.html         # 实时监测
├── visualization.html      # 数据可视化
├── analysis.html          # 用能分析
├── prediction.html        # AI预测
├── alerts.html            # 告警管理
├── control.html           # 控制策略
├── equipment.html         # 设备管理
├── reports.html           # 报表中心
├── carbon.html            # 碳资产管理
├── system.html            # 系统管理
├── assets/
│   ├── css/
│   │   └── common.css     # 通用样式
│   └── js/
│       ├── common.js      # 通用功能
│       └── charts.js      # 图表组件
└── README.md              # 项目说明
```

## 快速开始

1. **下载项目文件**
   ```bash
   # 将所有文件下载到本地目录
   ```

2. **启动本地服务器**
   ```bash
   # 使用Python启动简单HTTP服务器
   python -m http.server 8000
   
   # 或使用Node.js的http-server
   npx http-server
   ```

3. **访问系统**
   ```
   打开浏览器访问: http://localhost:8000
   ```

## 页面功能说明

### 导航系统
- 左侧固定导航栏，支持多级菜单
- 顶部面包屑导航
- 响应式设计，移动端自适应

### 数据展示
- 实时数据更新（模拟）
- 多种图表类型：折线图、柱状图、饼图、仪表盘
- 交互式地图展示

### 用户交互
- 表单验证和提交
- 模态框弹窗
- 通知消息系统
- 数据筛选和搜索

## 设计特色

### 视觉设计
- **色彩方案**: 以蓝色(#1890ff)为主色调，绿色(#52c41a)为辅助色
- **字体**: 使用系统默认字体栈，确保跨平台兼容性
- **图标**: Font Awesome 6.0 图标库
- **布局**: Grid + Flexbox 响应式布局

### 交互设计
- 悬停效果和过渡动画
- 直观的状态指示
- 一致的操作反馈
- 无障碍访问支持

### 数据可视化
- Chart.js 图表库
- 自定义仪表盘组件
- 实时数据更新动画
- 多维度数据展示

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 开发说明

### 自定义样式
所有样式都在 `assets/css/common.css` 中定义，包含：
- CSS变量定义
- 响应式断点
- 通用组件样式
- 工具类

### JavaScript功能
- `assets/js/common.js`: 通用功能和导航系统
- `assets/js/charts.js`: 图表组件和数据可视化
- 每个页面都有独立的JavaScript逻辑

### 数据模拟
系统使用模拟数据进行演示，包括：
- 实时能耗数据
- 设备状态信息
- 历史趋势数据
- 告警和日志信息

## 部署建议

### 生产环境
1. 使用CDN加速静态资源
2. 启用Gzip压缩
3. 配置缓存策略
4. 添加HTTPS支持

### 集成后端
1. 替换模拟数据为真实API调用
2. 添加用户认证和授权
3. 实现数据持久化
4. 添加实时数据推送

## 许可证

本项目仅用于演示和学习目的。

## 联系方式

如有问题或建议，请联系开发团队。

---

**注意**: 这是一个前端原型系统，所有数据都是模拟生成的。在实际使用中需要连接真实的后端服务和数据源。
