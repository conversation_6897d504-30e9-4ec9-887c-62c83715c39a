<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>充电桩管理 - 智慧能源管理平台</title>
    
    <!-- 外部CSS库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    
    <!-- 外部JS库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        /* 充电桩管理页面特定样式 */
        .charging-tabs {
            display: flex;
            background: white;
            border-radius: 8px 8px 0 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 0;
        }
        
        .tab-button {
            flex: 1;
            padding: 16px 24px;
            border: none;
            background: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #8c8c8c;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }
        
        .tab-button.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
            background: #f8f9fa;
        }
        
        .tab-content {
            background: white;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 24px;
            min-height: 600px;
        }
        
        .charging-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .charging-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #1890ff;
        }
        
        .charging-card.warning {
            border-left-color: #faad14;
        }
        
        .charging-card.error {
            border-left-color: #f5222d;
        }
        
        .charging-card.success {
            border-left-color: #52c41a;
        }
        
        .charging-card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }
        
        .charging-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: #1890ff;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        
        .charging-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin: 8px 0;
        }
        
        .charging-unit {
            font-size: 14px;
            color: #8c8c8c;
        }
        
        .charging-trend {
            font-size: 12px;
            margin-top: 4px;
        }
        
        .trend-up {
            color: #52c41a;
        }
        
        .trend-down {
            color: #f5222d;
        }
        
        .charging-station-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .station-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 2px solid transparent;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .station-card:hover {
            border-color: #1890ff;
            transform: translateY(-2px);
        }
        
        .station-card.available {
            border-left: 4px solid #52c41a;
        }
        
        .station-card.occupied {
            border-left: 4px solid #faad14;
        }
        
        .station-card.offline {
            border-left: 4px solid #f5222d;
        }
        
        .station-header {
            display: flex;
            align-items: center;
            justify-content: between;
            margin-bottom: 16px;
        }
        
        .station-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            margin-left: auto;
        }
        
        .status-available {
            background: #f6ffed;
            color: #52c41a;
        }
        
        .status-occupied {
            background: #fffbe6;
            color: #faad14;
        }
        
        .status-offline {
            background: #fff2f0;
            color: #f5222d;
        }
        
        .station-info {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 16px;
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
        }
        
        .info-label {
            font-size: 12px;
            color: #8c8c8c;
            margin-bottom: 4px;
        }
        
        .info-value {
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }
        
        .station-actions {
            display: flex;
            gap: 8px;
        }
        
        .btn-reserve {
            flex: 1;
            padding: 8px 16px;
            border: 1px solid #1890ff;
            background: #1890ff;
            color: white;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }
        
        .btn-reserve:hover {
            background: #40a9ff;
        }
        
        .btn-reserve:disabled {
            background: #d9d9d9;
            border-color: #d9d9d9;
            cursor: not-allowed;
        }
        
        .charging-progress {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
        }
        
        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e8e8e8;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 8px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #52c41a, #1890ff);
            transition: width 0.3s;
        }
        
        .progress-info {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #8c8c8c;
        }
        
        .map-container {
            height: 400px;
            background: #f0f2f5;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        .charging-map {
            width: 100%;
            height: 100%;
            position: relative;
            background: linear-gradient(45deg, #f0f2f5 25%, transparent 25%), 
                        linear-gradient(-45deg, #f0f2f5 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #f0f2f5 75%), 
                        linear-gradient(-45deg, transparent 75%, #f0f2f5 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
        
        .map-station {
            position: absolute;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .map-station:hover {
            transform: scale(1.2);
        }
        
        .map-station.available {
            background: #52c41a;
        }
        
        .map-station.occupied {
            background: #faad14;
        }
        
        .map-station.offline {
            background: #f5222d;
        }
        
        .payment-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .payment-card {
            background: white;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .payment-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        
        .payment-icon {
            font-size: 32px;
            margin-bottom: 8px;
        }
        
        .wechat { color: #07c160; }
        .alipay { color: #1677ff; }
        .card { color: #722ed1; }

        /* 控制卡片样式 */
        .control-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .control-switch {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .switch {
            position: relative;
            width: 50px;
            height: 24px;
            background: #ccc;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .switch.active {
            background: #52c41a;
        }

        .switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s;
        }

        .switch.active::after {
            transform: translateX(26px);
        }

        /* 表格样式 */
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 16px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .table th {
            background: #fafafa;
            font-weight: 500;
            color: #333;
        }

        .table-responsive {
            overflow-x: auto;
        }

        /* 表单控件样式 */
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        /* 按钮样式增强 */
        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
        }

        .btn-danger {
            background: #f5222d;
            border-color: #f5222d;
            color: white;
        }

        .btn-danger:hover {
            background: #ff4d4f;
        }

        .btn-warning {
            background: #faad14;
            border-color: #faad14;
            color: white;
        }

        .btn-warning:hover {
            background: #ffc53d;
        }

        /* 状态指示器 */
        .status-info {
            background: #e6f7ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }
        
        @media (max-width: 768px) {
            .charging-overview {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
            
            .charging-station-grid {
                grid-template-columns: 1fr;
            }
            
            .charging-tabs {
                flex-direction: column;
            }
            
            .tab-button {
                border-bottom: none;
                border-right: 3px solid transparent;
            }
            
            .tab-button.active {
                border-right-color: #1890ff;
                border-bottom-color: transparent;
            }
        }
    </style>
</head>
<body>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化页面布局
            initializeLayout('充电桩管理', ['运营中心', '充电桩管理']);
            
            // 获取页面内容容器
            const pageContent = document.getElementById('page-content');
            
            // 插入页面内容
            pageContent.innerHTML = `
                <!-- 标签页导航 -->
                <div class="charging-tabs">
                    <button class="tab-button active" onclick="switchTab('overview')">
                        <i class="fas fa-tachometer-alt" style="margin-right: 8px;"></i>
                        总览监控
                    </button>
                    <button class="tab-button" onclick="switchTab('reservation')">
                        <i class="fas fa-calendar-alt" style="margin-right: 8px;"></i>
                        预约管理
                    </button>
                    <button class="tab-button" onclick="switchTab('monitoring')">
                        <i class="fas fa-chart-line" style="margin-right: 8px;"></i>
                        实时监控
                    </button>
                    <button class="tab-button" onclick="switchTab('payment')">
                        <i class="fas fa-credit-card" style="margin-right: 8px;"></i>
                        支付管理
                    </button>
                    <button class="tab-button" onclick="switchTab('security')">
                        <i class="fas fa-shield-alt" style="margin-right: 8px;"></i>
                        安全管控
                    </button>
                </div>

                <!-- 标签页内容 -->
                <div class="tab-content">
                    <div id="overview-content">
                        <!-- 总览监控内容将在这里插入 -->
                    </div>
                    <div id="reservation-content" style="display: none;">
                        <!-- 预约管理内容将在这里插入 -->
                    </div>
                    <div id="monitoring-content" style="display: none;">
                        <!-- 实时监控内容将在这里插入 -->
                    </div>
                    <div id="payment-content" style="display: none;">
                        <!-- 支付管理内容将在这里插入 -->
                    </div>
                    <div id="security-content" style="display: none;">
                        <!-- 安全管控内容将在这里插入 -->
                    </div>
                </div>
            `;
            
            // 初始化充电桩管理页面
            initializeChargingManagement();
        });

        // 切换标签页
        function switchTab(tabName) {
            // 更新标签按钮状态
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // 隐藏所有内容
            document.querySelectorAll('[id$="-content"]').forEach(content => {
                content.style.display = 'none';
            });

            // 显示选中的内容
            document.getElementById(tabName + '-content').style.display = 'block';

            // 根据标签页加载相应内容
            switch(tabName) {
                case 'overview':
                    loadOverviewContent();
                    break;
                case 'reservation':
                    loadReservationContent();
                    break;
                case 'monitoring':
                    loadMonitoringContent();
                    break;
                case 'payment':
                    loadPaymentContent();
                    break;
                case 'security':
                    loadSecurityContent();
                    break;
            }
        }

        // 初始化充电桩管理页面
        function initializeChargingManagement() {
            loadOverviewContent();
        }

        // 加载总览监控内容
        function loadOverviewContent() {
            const content = document.getElementById('overview-content');
            content.innerHTML = `
                <!-- 充电桩概览卡片 -->
                <div class="charging-overview">
                    <div class="charging-card success">
                        <div class="charging-card-header">
                            <div class="charging-icon" style="background: #52c41a;">
                                <i class="fas fa-charging-station"></i>
                            </div>
                            <span class="status-tag status-success">正常</span>
                        </div>
                        <div class="charging-value">24 <span class="charging-unit">个</span></div>
                        <div class="charging-trend trend-up">
                            <i class="fas fa-arrow-up"></i> 总充电桩数量
                        </div>
                    </div>

                    <div class="charging-card success">
                        <div class="charging-card-header">
                            <div class="charging-icon" style="background: #52c41a;">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <span class="status-tag status-success">空闲</span>
                        </div>
                        <div class="charging-value">18 <span class="charging-unit">个</span></div>
                        <div class="charging-trend">
                            <i class="fas fa-info-circle"></i> 可用充电桩
                        </div>
                    </div>

                    <div class="charging-card warning">
                        <div class="charging-card-header">
                            <div class="charging-icon" style="background: #faad14;">
                                <i class="fas fa-battery-half"></i>
                            </div>
                            <span class="status-tag status-warning">使用中</span>
                        </div>
                        <div class="charging-value">5 <span class="charging-unit">个</span></div>
                        <div class="charging-trend">
                            <i class="fas fa-clock"></i> 正在充电
                        </div>
                    </div>

                    <div class="charging-card error">
                        <div class="charging-card-header">
                            <div class="charging-icon" style="background: #f5222d;">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <span class="status-tag status-danger">故障</span>
                        </div>
                        <div class="charging-value">1 <span class="charging-unit">个</span></div>
                        <div class="charging-trend">
                            <i class="fas fa-tools"></i> 需要维修
                        </div>
                    </div>
                </div>

                <!-- 充电桩地图 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-map-marked-alt" style="margin-right: 8px;"></i>
                            充电桩分布图
                        </h3>
                        <div>
                            <button class="btn btn-primary" onclick="refreshMap()">
                                <i class="fas fa-sync-alt"></i>
                                刷新
                            </button>
                            <button class="btn btn-primary" onclick="toggleMapView()">
                                <i class="fas fa-expand"></i>
                                全屏
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="map-container" id="chargingMap">
                            <div class="charging-map" id="mapCanvas">
                                <!-- 充电桩位置将通过JavaScript动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 充电桩列表 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">充电桩状态列表</h3>
                        <button class="btn btn-primary" onclick="refreshStationList()">
                            <i class="fas fa-sync-alt"></i>
                            刷新状态
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="charging-station-grid" id="stationGrid">
                            <!-- 充电桩卡片将通过JavaScript动态插入 -->
                        </div>
                    </div>
                </div>
            `;

            // 初始化地图和充电桩列表
            initializeChargingMap();
            loadStationList();
        }

        // 加载预约管理内容
        function loadReservationContent() {
            const content = document.getElementById('reservation-content');
            content.innerHTML = `
                <!-- 预约统计 -->
                <div class="charging-overview">
                    <div class="charging-card">
                        <div class="charging-card-header">
                            <div class="charging-icon">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <span class="status-tag status-success">今日</span>
                        </div>
                        <div class="charging-value">12 <span class="charging-unit">个</span></div>
                        <div class="charging-trend">今日预约数量</div>
                    </div>

                    <div class="charging-card warning">
                        <div class="charging-card-header">
                            <div class="charging-icon" style="background: #faad14;">
                                <i class="fas fa-clock"></i>
                            </div>
                            <span class="status-tag status-warning">进行中</span>
                        </div>
                        <div class="charging-value">5 <span class="charging-unit">个</span></div>
                        <div class="charging-trend">正在使用</div>
                    </div>

                    <div class="charging-card success">
                        <div class="charging-card-header">
                            <div class="charging-icon" style="background: #52c41a;">
                                <i class="fas fa-percentage"></i>
                            </div>
                            <span class="status-tag status-success">高效</span>
                        </div>
                        <div class="charging-value">92.5 <span class="charging-unit">%</span></div>
                        <div class="charging-trend">预约使用率</div>
                    </div>
                </div>

                <!-- 快速预约 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-plus-circle" style="margin-right: 8px;"></i>
                            快速预约
                        </h3>
                    </div>
                    <div class="card-body">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                            <div>
                                <label style="display: block; margin-bottom: 8px;">选择充电桩：</label>
                                <select class="form-control" id="stationSelect" onchange="updateStationInfo()">
                                    <option value="">请选择充电桩</option>
                                    <option value="A01">A01 - 快充桩 (可用)</option>
                                    <option value="A02">A02 - 快充桩 (可用)</option>
                                    <option value="B01">B01 - 慢充桩 (可用)</option>
                                    <option value="B02">B02 - 慢充桩 (使用中)</option>
                                </select>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 8px;">预约时间：</label>
                                <input type="datetime-local" class="form-control" id="reservationTime">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 8px;">预计充电时长：</label>
                                <select class="form-control" id="chargingDuration">
                                    <option value="30">30分钟</option>
                                    <option value="60">1小时</option>
                                    <option value="120">2小时</option>
                                    <option value="240">4小时</option>
                                    <option value="480">8小时</option>
                                </select>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 8px;">车辆类型：</label>
                                <select class="form-control" id="vehicleType">
                                    <option value="car">电动汽车</option>
                                    <option value="bike">电动自行车</option>
                                    <option value="device">移动设备</option>
                                </select>
                            </div>
                        </div>
                        <div style="margin-top: 20px; text-align: center;">
                            <button class="btn btn-primary" onclick="makeReservation()" style="padding: 12px 32px;">
                                <i class="fas fa-calendar-plus"></i>
                                立即预约
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 预约列表 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">预约记录</h3>
                        <div>
                            <button class="btn btn-primary" onclick="refreshReservations()">
                                <i class="fas fa-sync-alt"></i>
                                刷新
                            </button>
                            <button class="btn btn-warning" onclick="exportReservations()">
                                <i class="fas fa-download"></i>
                                导出
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>预约编号</th>
                                        <th>充电桩</th>
                                        <th>用户</th>
                                        <th>预约时间</th>
                                        <th>充电时长</th>
                                        <th>车辆类型</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="reservationTable">
                                    <!-- 预约数据将通过JavaScript动态插入 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;

            loadReservationList();
        }

        // 加载实时监控内容
        function loadMonitoringContent() {
            const content = document.getElementById('monitoring-content');
            content.innerHTML = `
                <!-- 实时监控图表 -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 24px;">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">充电功率趋势</h3>
                            <button class="btn btn-primary" onclick="refreshPowerChart()">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                        <div class="card-body">
                            <div style="height: 300px; position: relative;">
                                <canvas id="chargingPowerChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">充电桩使用率</h3>
                        </div>
                        <div class="card-body">
                            <div style="height: 300px; position: relative;">
                                <canvas id="utilizationChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 实时充电状态 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">实时充电状态</h3>
                        <button class="btn btn-primary" onclick="refreshChargingStatus()">
                            <i class="fas fa-sync-alt"></i>
                            刷新状态
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="chargingStatusList">
                            <!-- 充电状态将通过JavaScript动态插入 -->
                        </div>
                    </div>
                </div>

                <!-- 设备参数监控 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">设备参数监控</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>充电桩编号</th>
                                        <th>电压 (V)</th>
                                        <th>电流 (A)</th>
                                        <th>功率 (kW)</th>
                                        <th>温度 (°C)</th>
                                        <th>状态</th>
                                        <th>更新时间</th>
                                    </tr>
                                </thead>
                                <tbody id="deviceParametersTable">
                                    <!-- 设备参数数据将通过JavaScript动态插入 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;

            // 初始化监控图表
            initializeChargingPowerChart();
            initializeUtilizationChart();
            loadChargingStatus();
            loadDeviceParameters();
        }

        // 加载支付管理内容
        function loadPaymentContent() {
            const content = document.getElementById('payment-content');
            content.innerHTML = `
                <!-- 支付方式 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-credit-card" style="margin-right: 8px;"></i>
                            支付方式管理
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="payment-methods">
                            <div class="payment-card" onclick="selectPaymentMethod('wechat')">
                                <div class="payment-icon wechat">
                                    <i class="fab fa-weixin"></i>
                                </div>
                                <h4>微信支付</h4>
                                <p>扫码支付，便捷安全</p>
                            </div>
                            <div class="payment-card" onclick="selectPaymentMethod('alipay')">
                                <div class="payment-icon alipay">
                                    <i class="fab fa-alipay"></i>
                                </div>
                                <h4>支付宝</h4>
                                <p>快速支付，实时到账</p>
                            </div>
                            <div class="payment-card" onclick="selectPaymentMethod('card')">
                                <div class="payment-icon card">
                                    <i class="fas fa-id-card"></i>
                                </div>
                                <h4>工卡支付</h4>
                                <p>员工专享，自动扣费</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 费用统计 -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 24px;">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">费用统计</h3>
                        </div>
                        <div class="card-body">
                            <div style="height: 300px; position: relative;">
                                <canvas id="feeChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">支付方式分布</h3>
                        </div>
                        <div class="card-body">
                            <div style="height: 300px; position: relative;">
                                <canvas id="paymentDistributionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 费用明细 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">费用明细</h3>
                        <div>
                            <button class="btn btn-primary" onclick="refreshFeeDetails()">
                                <i class="fas fa-sync-alt"></i>
                                刷新
                            </button>
                            <button class="btn btn-warning" onclick="exportFeeDetails()">
                                <i class="fas fa-download"></i>
                                导出账单
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>订单号</th>
                                        <th>用户</th>
                                        <th>充电桩</th>
                                        <th>充电时长</th>
                                        <th>充电量 (kWh)</th>
                                        <th>费用 (元)</th>
                                        <th>支付方式</th>
                                        <th>支付时间</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody id="feeDetailsTable">
                                    <!-- 费用明细数据将通过JavaScript动态插入 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;

            // 初始化支付图表
            initializeFeeChart();
            initializePaymentDistributionChart();
            loadFeeDetails();
        }

        // 加载安全管控内容
        function loadSecurityContent() {
            const content = document.getElementById('security-content');
            content.innerHTML = `
                <!-- 安全状态概览 -->
                <div class="charging-overview">
                    <div class="charging-card success">
                        <div class="charging-card-header">
                            <div class="charging-icon" style="background: #52c41a;">
                                <i class="fas fa-shield-check"></i>
                            </div>
                            <span class="status-tag status-success">正常</span>
                        </div>
                        <div class="charging-value">23 <span class="charging-unit">个</span></div>
                        <div class="charging-trend">安全运行设备</div>
                    </div>

                    <div class="charging-card warning">
                        <div class="charging-card-header">
                            <div class="charging-icon" style="background: #faad14;">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <span class="status-tag status-warning">警告</span>
                        </div>
                        <div class="charging-value">1 <span class="charging-unit">个</span></div>
                        <div class="charging-trend">温度异常</div>
                    </div>

                    <div class="charging-card">
                        <div class="charging-card-header">
                            <div class="charging-icon" style="background: #1890ff;">
                                <i class="fas fa-car"></i>
                            </div>
                            <span class="status-tag status-info">监控中</span>
                        </div>
                        <div class="charging-value">0 <span class="charging-unit">起</span></div>
                        <div class="charging-trend">占位违规事件</div>
                    </div>

                    <div class="charging-card success">
                        <div class="charging-card-header">
                            <div class="charging-icon" style="background: #722ed1;">
                                <i class="fas fa-eye"></i>
                            </div>
                            <span class="status-tag status-success">在线</span>
                        </div>
                        <div class="charging-value">24 <span class="charging-unit">个</span></div>
                        <div class="charging-trend">AI监控摄像头</div>
                    </div>
                </div>

                <!-- 安全保护功能 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-shield-alt" style="margin-right: 8px;"></i>
                            安全保护功能
                        </h3>
                    </div>
                    <div class="card-body">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                            <div class="control-card">
                                <h4>电压保护</h4>
                                <div class="control-switch">
                                    <span>过压保护 (>250V)</span>
                                    <div class="switch active" onclick="toggleSwitch(this, 'overvoltage')"></div>
                                </div>
                                <div class="control-switch">
                                    <span>欠压保护 (<180V)</span>
                                    <div class="switch active" onclick="toggleSwitch(this, 'undervoltage')"></div>
                                </div>
                            </div>

                            <div class="control-card">
                                <h4>电流保护</h4>
                                <div class="control-switch">
                                    <span>过载保护</span>
                                    <div class="switch active" onclick="toggleSwitch(this, 'overload')"></div>
                                </div>
                                <div class="control-switch">
                                    <span>短路保护</span>
                                    <div class="switch active" onclick="toggleSwitch(this, 'shortcircuit')"></div>
                                </div>
                            </div>

                            <div class="control-card">
                                <h4>温度保护</h4>
                                <div class="control-switch">
                                    <span>过温保护 (>60°C)</span>
                                    <div class="switch active" onclick="toggleSwitch(this, 'overtemp')"></div>
                                </div>
                                <div class="control-switch">
                                    <span>漏电保护</span>
                                    <div class="switch active" onclick="toggleSwitch(this, 'leakage')"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AI防占位监控 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">AI防占位监控</h3>
                        <div>
                            <button class="btn btn-primary" onclick="refreshAIMonitoring()">
                                <i class="fas fa-sync-alt"></i>
                                刷新
                            </button>
                            <button class="btn btn-warning" onclick="testAIAlert()">
                                <i class="fas fa-bell"></i>
                                测试告警
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="aiMonitoringList">
                            <!-- AI监控数据将通过JavaScript动态插入 -->
                        </div>
                    </div>
                </div>

                <!-- 安全事件日志 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">安全事件日志</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>时间</th>
                                        <th>事件类型</th>
                                        <th>充电桩</th>
                                        <th>描述</th>
                                        <th>处理状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="securityEventsTable">
                                    <!-- 安全事件数据将通过JavaScript动态插入 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;

            loadAIMonitoring();
            loadSecurityEvents();
        }

        // 初始化充电桩地图
        function initializeChargingMap() {
            const mapCanvas = document.getElementById('mapCanvas');
            if (!mapCanvas) return;

            // 清空现有内容
            mapCanvas.innerHTML = '';

            // 生成充电桩位置
            const stations = [
                { id: 'A01', x: 20, y: 30, status: 'available' },
                { id: 'A02', x: 80, y: 30, status: 'available' },
                { id: 'A03', x: 140, y: 30, status: 'occupied' },
                { id: 'B01', x: 20, y: 100, status: 'available' },
                { id: 'B02', x: 80, y: 100, status: 'occupied' },
                { id: 'B03', x: 140, y: 100, status: 'offline' },
                { id: 'C01', x: 20, y: 170, status: 'available' },
                { id: 'C02', x: 80, y: 170, status: 'available' },
                { id: 'C03', x: 140, y: 170, status: 'occupied' }
            ];

            stations.forEach(station => {
                const stationElement = document.createElement('div');
                stationElement.className = `map-station ${station.status}`;
                stationElement.style.left = station.x + 'px';
                stationElement.style.top = station.y + 'px';
                stationElement.textContent = station.id;
                stationElement.onclick = () => showStationDetails(station.id);
                mapCanvas.appendChild(stationElement);
            });
        }

        // 显示充电桩详情
        function showStationDetails(stationId) {
            const stationData = getStationData(stationId);
            showNotification(`充电桩 ${stationId}: ${stationData.status}`, 'info');
        }

        // 获取充电桩数据
        function getStationData(stationId) {
            const mockData = {
                'A01': { status: '空闲', type: '快充', power: '60kW', voltage: '220V' },
                'A02': { status: '空闲', type: '快充', power: '60kW', voltage: '220V' },
                'A03': { status: '使用中', type: '快充', power: '60kW', voltage: '220V' },
                'B01': { status: '空闲', type: '慢充', power: '7kW', voltage: '220V' },
                'B02': { status: '使用中', type: '慢充', power: '7kW', voltage: '220V' },
                'B03': { status: '故障', type: '慢充', power: '7kW', voltage: '220V' }
            };
            return mockData[stationId] || { status: '未知', type: '未知', power: '0kW', voltage: '0V' };
        }

        // 加载充电桩列表
        function loadStationList() {
            const grid = document.getElementById('stationGrid');
            if (!grid) return;

            const stations = [
                { id: 'A01', type: '直流快充', power: '60kW', status: 'available', location: 'A区1号位' },
                { id: 'A02', type: '直流快充', power: '60kW', status: 'available', location: 'A区2号位' },
                { id: 'A03', type: '直流快充', power: '60kW', status: 'occupied', location: 'A区3号位' },
                { id: 'B01', type: '交流慢充', power: '7kW', status: 'available', location: 'B区1号位' },
                { id: 'B02', type: '交流慢充', power: '7kW', status: 'occupied', location: 'B区2号位' },
                { id: 'B03', type: '交流慢充', power: '7kW', status: 'offline', location: 'B区3号位' }
            ];

            grid.innerHTML = stations.map(station => `
                <div class="station-card ${station.status}" onclick="selectStation('${station.id}')">
                    <div class="station-header">
                        <h4>${station.id}</h4>
                        <span class="station-status status-${station.status}">
                            ${station.status === 'available' ? '空闲' :
                              station.status === 'occupied' ? '使用中' : '离线'}
                        </span>
                    </div>
                    <div class="station-info">
                        <div class="info-item">
                            <div class="info-label">充电类型</div>
                            <div class="info-value">${station.type}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">最大功率</div>
                            <div class="info-value">${station.power}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">位置</div>
                            <div class="info-value">${station.location}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">状态</div>
                            <div class="info-value">${station.status === 'available' ? '可预约' :
                                                    station.status === 'occupied' ? '使用中' : '维护中'}</div>
                        </div>
                    </div>
                    <div class="station-actions">
                        <button class="btn-reserve" ${station.status !== 'available' ? 'disabled' : ''}
                                onclick="reserveStation('${station.id}')">
                            ${station.status === 'available' ? '立即预约' : '不可预约'}
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // 选择充电桩
        function selectStation(stationId) {
            showNotification(`已选择充电桩 ${stationId}`, 'info');
        }

        // 预约充电桩
        function reserveStation(stationId) {
            if (confirm(`确定要预约充电桩 ${stationId} 吗？`)) {
                showNotification(`充电桩 ${stationId} 预约成功！`, 'success');
                // 这里可以添加实际的预约逻辑
            }
        }

        // 加载预约列表
        function loadReservationList() {
            const tbody = document.getElementById('reservationTable');
            if (!tbody) return;

            const reservations = [
                {
                    id: 'R001',
                    station: 'A01',
                    user: '张三',
                    time: '2024-01-15 14:00',
                    duration: '2小时',
                    vehicle: '电动汽车',
                    status: '进行中'
                },
                {
                    id: 'R002',
                    station: 'B01',
                    user: '李四',
                    time: '2024-01-15 15:30',
                    duration: '1小时',
                    vehicle: '电动自行车',
                    status: '已完成'
                },
                {
                    id: 'R003',
                    station: 'A02',
                    user: '王五',
                    time: '2024-01-15 16:00',
                    duration: '3小时',
                    vehicle: '电动汽车',
                    status: '待开始'
                }
            ];

            tbody.innerHTML = reservations.map(reservation => `
                <tr>
                    <td>${reservation.id}</td>
                    <td>${reservation.station}</td>
                    <td>${reservation.user}</td>
                    <td>${reservation.time}</td>
                    <td>${reservation.duration}</td>
                    <td>${reservation.vehicle}</td>
                    <td>
                        <span class="status-tag ${getReservationStatusClass(reservation.status)}">
                            ${reservation.status}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-sm" onclick="viewReservation('${reservation.id}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="cancelReservation('${reservation.id}')">
                            <i class="fas fa-times"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // 获取预约状态样式类
        function getReservationStatusClass(status) {
            switch(status) {
                case '进行中': return 'status-warning';
                case '已完成': return 'status-success';
                case '待开始': return 'status-info';
                default: return 'status-info';
            }
        }

        // 初始化充电功率图表
        function initializeChargingPowerChart() {
            const ctx = document.getElementById('chargingPowerChart');
            if (!ctx) return;

            const data = {
                labels: Array.from({length: 24}, (_, i) => `${i.toString().padStart(2, '0')}:00`),
                datasets: [{
                    label: '总充电功率 (kW)',
                    data: generateChargingPowerData(),
                    borderColor: '#1890ff',
                    backgroundColor: 'rgba(24, 144, 255, 0.1)',
                    fill: true,
                    tension: 0.4
                }]
            };

            window.chargingPowerChart = createLineChart('chargingPowerChart', data);
        }

        // 初始化使用率图表
        function initializeUtilizationChart() {
            const ctx = document.getElementById('utilizationChart');
            if (!ctx) return;

            const data = {
                labels: ['快充桩', '慢充桩', '移动设备充电'],
                datasets: [{
                    data: [75, 60, 45],
                    backgroundColor: ['#1890ff', '#52c41a', '#faad14']
                }]
            };

            createPieChart('utilizationChart', data);
        }

        // 生成充电功率数据
        function generateChargingPowerData() {
            const data = [];
            for (let i = 0; i < 24; i++) {
                // 模拟一天的充电功率变化
                if (i < 6) {
                    data.push(Math.random() * 50 + 20); // 夜间低功率
                } else if (i < 9) {
                    data.push(Math.random() * 150 + 100); // 早高峰
                } else if (i < 17) {
                    data.push(Math.random() * 100 + 80); // 白天正常
                } else if (i < 20) {
                    data.push(Math.random() * 180 + 120); // 晚高峰
                } else {
                    data.push(Math.random() * 80 + 40); // 夜间
                }
            }
            return data;
        }

        // 加载充电状态
        function loadChargingStatus() {
            const statusList = document.getElementById('chargingStatusList');
            if (!statusList) return;

            const chargingData = [
                {
                    station: 'A03',
                    user: '张三',
                    vehicle: '电动汽车',
                    startTime: '14:30',
                    progress: 65,
                    estimatedTime: '35分钟',
                    power: '45kW',
                    energy: '28.5kWh'
                },
                {
                    station: 'B02',
                    user: '李四',
                    vehicle: '电动自行车',
                    startTime: '15:15',
                    progress: 80,
                    estimatedTime: '15分钟',
                    power: '3.5kW',
                    energy: '2.8kWh'
                }
            ];

            statusList.innerHTML = chargingData.map(item => `
                <div class="charging-progress">
                    <div class="progress-header">
                        <h4>充电桩 ${item.station} - ${item.user}</h4>
                        <span>${item.vehicle}</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${item.progress}%"></div>
                    </div>
                    <div class="progress-info">
                        <span>开始时间: ${item.startTime}</span>
                        <span>预计剩余: ${item.estimatedTime}</span>
                    </div>
                    <div class="progress-info">
                        <span>当前功率: ${item.power}</span>
                        <span>已充电量: ${item.energy}</span>
                    </div>
                </div>
            `).join('');
        }

        // 加载设备参数
        function loadDeviceParameters() {
            const tbody = document.getElementById('deviceParametersTable');
            if (!tbody) return;

            const parameters = [
                { id: 'A01', voltage: '220.5', current: '0.0', power: '0.0', temp: '25.3', status: '空闲' },
                { id: 'A02', voltage: '220.8', current: '0.0', power: '0.0', temp: '26.1', status: '空闲' },
                { id: 'A03', voltage: '221.2', current: '204.5', power: '45.2', temp: '42.8', status: '充电中' },
                { id: 'B01', voltage: '220.1', current: '0.0', power: '0.0', temp: '24.9', status: '空闲' },
                { id: 'B02', voltage: '219.8', current: '15.9', power: '3.5', temp: '31.2', status: '充电中' },
                { id: 'B03', voltage: '0.0', current: '0.0', power: '0.0', temp: '0.0', status: '离线' }
            ];

            tbody.innerHTML = parameters.map(param => `
                <tr>
                    <td>${param.id}</td>
                    <td>${param.voltage}</td>
                    <td>${param.current}</td>
                    <td>${param.power}</td>
                    <td>${param.temp}</td>
                    <td>
                        <span class="status-tag ${getDeviceStatusClass(param.status)}">${param.status}</span>
                    </td>
                    <td>${new Date().toLocaleString()}</td>
                </tr>
            `).join('');
        }

        // 获取设备状态样式类
        function getDeviceStatusClass(status) {
            switch(status) {
                case '空闲': return 'status-success';
                case '充电中': return 'status-warning';
                case '离线': return 'status-danger';
                default: return 'status-info';
            }
        }

        // 初始化费用图表
        function initializeFeeChart() {
            const ctx = document.getElementById('feeChart');
            if (!ctx) return;

            const data = {
                labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                datasets: [{
                    label: '充电费用 (元)',
                    data: [1250, 1380, 1420, 1180, 1650, 890, 750],
                    borderColor: '#52c41a',
                    backgroundColor: 'rgba(82, 196, 26, 0.1)',
                    fill: true,
                    tension: 0.4
                }]
            };

            createLineChart('feeChart', data);
        }

        // 初始化支付方式分布图表
        function initializePaymentDistributionChart() {
            const ctx = document.getElementById('paymentDistributionChart');
            if (!ctx) return;

            const data = {
                labels: ['微信支付', '支付宝', '工卡支付'],
                datasets: [{
                    data: [45, 30, 25],
                    backgroundColor: ['#07c160', '#1677ff', '#722ed1']
                }]
            };

            createPieChart('paymentDistributionChart', data);
        }

        // 加载费用明细
        function loadFeeDetails() {
            const tbody = document.getElementById('feeDetailsTable');
            if (!tbody) return;

            const feeDetails = [
                {
                    orderId: 'O001',
                    user: '张三',
                    station: 'A03',
                    duration: '2小时15分',
                    energy: '28.5',
                    fee: '45.60',
                    method: '微信支付',
                    time: '2024-01-15 16:45',
                    status: '已支付'
                },
                {
                    orderId: 'O002',
                    user: '李四',
                    station: 'B02',
                    duration: '1小时20分',
                    energy: '2.8',
                    fee: '5.60',
                    method: '工卡支付',
                    time: '2024-01-15 16:35',
                    status: '已支付'
                }
            ];

            tbody.innerHTML = feeDetails.map(detail => `
                <tr>
                    <td>${detail.orderId}</td>
                    <td>${detail.user}</td>
                    <td>${detail.station}</td>
                    <td>${detail.duration}</td>
                    <td>${detail.energy}</td>
                    <td>¥${detail.fee}</td>
                    <td>${detail.method}</td>
                    <td>${detail.time}</td>
                    <td>
                        <span class="status-tag status-success">${detail.status}</span>
                    </td>
                </tr>
            `).join('');
        }

        // 加载AI监控数据
        function loadAIMonitoring() {
            const aiList = document.getElementById('aiMonitoringList');
            if (!aiList) return;

            const aiData = [
                {
                    camera: '摄像头01',
                    location: 'A区充电区域',
                    status: '正常监控',
                    lastDetection: '无违规',
                    confidence: '99.2%'
                },
                {
                    camera: '摄像头02',
                    location: 'B区充电区域',
                    status: '正常监控',
                    lastDetection: '无违规',
                    confidence: '98.7%'
                }
            ];

            aiList.innerHTML = aiData.map(item => `
                <div class="charging-progress">
                    <div class="progress-header">
                        <h4>${item.camera} - ${item.location}</h4>
                        <span class="status-tag status-success">${item.status}</span>
                    </div>
                    <div class="progress-info">
                        <span>最近检测: ${item.lastDetection}</span>
                        <span>识别准确率: ${item.confidence}</span>
                    </div>
                </div>
            `).join('');
        }

        // 加载安全事件
        function loadSecurityEvents() {
            const tbody = document.getElementById('securityEventsTable');
            if (!tbody) return;

            const events = [
                {
                    time: '2024-01-15 14:25',
                    type: '温度告警',
                    station: 'A03',
                    description: '充电桩温度超过阈值',
                    status: '已处理'
                },
                {
                    time: '2024-01-15 13:45',
                    type: '过载保护',
                    station: 'B01',
                    description: '检测到过载，自动断电保护',
                    status: '已恢复'
                }
            ];

            tbody.innerHTML = events.map(event => `
                <tr>
                    <td>${event.time}</td>
                    <td>${event.type}</td>
                    <td>${event.station}</td>
                    <td>${event.description}</td>
                    <td>
                        <span class="status-tag status-success">${event.status}</span>
                    </td>
                    <td>
                        <button class="btn btn-sm" onclick="viewEvent('${event.time}')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // 切换开关
        function toggleSwitch(element, deviceId) {
            element.classList.toggle('active');
            const isActive = element.classList.contains('active');

            showNotification(`${deviceId} 保护功能已${isActive ? '开启' : '关闭'}`, 'success');
        }

        // 其他功能函数
        function makeReservation() {
            const station = document.getElementById('stationSelect').value;
            const time = document.getElementById('reservationTime').value;
            const duration = document.getElementById('chargingDuration').value;
            const vehicle = document.getElementById('vehicleType').value;

            if (!station || !time) {
                showNotification('请选择充电桩和预约时间', 'warning');
                return;
            }

            showNotification(`预约成功！充电桩: ${station}, 时间: ${time}`, 'success');
            loadReservationList();
        }

        function selectPaymentMethod(method) {
            showNotification(`已选择${method === 'wechat' ? '微信支付' : method === 'alipay' ? '支付宝' : '工卡支付'}`, 'info');
        }

        function refreshMap() {
            initializeChargingMap();
            showNotification('地图已刷新', 'success');
        }

        function refreshStationList() {
            loadStationList();
            showNotification('充电桩状态已刷新', 'success');
        }

        function refreshReservations() {
            loadReservationList();
            showNotification('预约列表已刷新', 'success');
        }

        function refreshChargingStatus() {
            loadChargingStatus();
            loadDeviceParameters();
            showNotification('充电状态已刷新', 'success');
        }

        function refreshPowerChart() {
            if (window.chargingPowerChart) {
                const newData = {
                    labels: Array.from({length: 24}, (_, i) => `${i.toString().padStart(2, '0')}:00`),
                    datasets: [{
                        label: '总充电功率 (kW)',
                        data: generateChargingPowerData(),
                        borderColor: '#1890ff',
                        backgroundColor: 'rgba(24, 144, 255, 0.1)',
                        fill: true,
                        tension: 0.4
                    }]
                };
                updateChartData(window.chargingPowerChart, newData);
                showNotification('功率图表已更新', 'success');
            }
        }

        function refreshFeeDetails() {
            loadFeeDetails();
            showNotification('费用明细已刷新', 'success');
        }

        function refreshAIMonitoring() {
            loadAIMonitoring();
            showNotification('AI监控数据已刷新', 'success');
        }

        function testAIAlert() {
            showNotification('AI告警测试：检测到燃油车占用充电位！', 'warning');
        }

        function exportReservations() {
            showNotification('预约数据导出功能开发中...', 'info');
        }

        function exportFeeDetails() {
            showNotification('费用账单导出功能开发中...', 'info');
        }

        function viewReservation(id) {
            showNotification(`查看预约 ${id} 详情`, 'info');
        }

        function cancelReservation(id) {
            if (confirm(`确定要取消预约 ${id} 吗？`)) {
                showNotification(`预约 ${id} 已取消`, 'success');
                loadReservationList();
            }
        }

        function viewEvent(time) {
            showNotification(`查看 ${time} 安全事件详情`, 'info');
        }

        function toggleMapView() {
            const container = document.getElementById('chargingMap');
            if (container.requestFullscreen) {
                container.requestFullscreen();
            }
        }

        function updateStationInfo() {
            const select = document.getElementById('stationSelect');
            const stationId = select.value;
            if (stationId) {
                const stationData = getStationData(stationId);
                showNotification(`已选择 ${stationId} - ${stationData.type}`, 'info');
            }
        }

        // 启动实时数据更新
        function startRealTimeUpdate() {
            setInterval(() => {
                // 更新设备参数
                if (document.getElementById('monitoring-content').style.display !== 'none') {
                    loadDeviceParameters();
                }

                // 更新充电状态
                if (document.getElementById('monitoring-content').style.display !== 'none') {
                    loadChargingStatus();
                }
            }, 30000); // 每30秒更新一次
        }

        // 页面加载完成后启动实时更新
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                startRealTimeUpdate();
            }, 5000); // 5秒后开始实时更新
        });

        // 添加键盘快捷键支持
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case '1':
                        e.preventDefault();
                        switchTab('overview');
                        break;
                    case '2':
                        e.preventDefault();
                        switchTab('reservation');
                        break;
                    case '3':
                        e.preventDefault();
                        switchTab('monitoring');
                        break;
                    case '4':
                        e.preventDefault();
                        switchTab('payment');
                        break;
                    case '5':
                        e.preventDefault();
                        switchTab('security');
                        break;
                    case 'r':
                        e.preventDefault();
                        location.reload();
                        break;
                }
            }
        });
    </script>

    <!-- 引入公共脚本 -->
    <script src="assets/js/common.js"></script>
    <script src="assets/js/charts.js"></script>
</body>
</html>
