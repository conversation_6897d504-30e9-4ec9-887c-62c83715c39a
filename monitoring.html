<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时监测 - 智慧能源管理平台</title>
    
    <!-- 外部CSS库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    
    <!-- 外部JS库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        .monitoring-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin-bottom: 24px;
        }
        
        .gauge-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .gauge-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .gauge-chart {
            width: 150px;
            height: 100px;
            margin: 0 auto 16px;
        }
        
        .meter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .meter-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .meter-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }
        
        .meter-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .meter-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .meter-readings {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }
        
        .reading-item {
            text-align: center;
        }
        
        .reading-value {
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
        }
        
        .reading-label {
            font-size: 12px;
            color: #8c8c8c;
            margin-top: 4px;
        }
        
        .real-time-table {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .table-header {
            background: #fafafa;
            padding: 16px 20px;
            border-bottom: 1px solid #d9d9d9;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .auto-refresh {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #52c41a;
        }
        
        .blink {
            animation: blink 1s infinite;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }
        
        @media (max-width: 768px) {
            .monitoring-grid {
                grid-template-columns: 1fr;
            }
            
            .gauge-container {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .meter-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化页面布局
            initializeLayout('实时监测', ['监测中心', '实时监测']);
            
            // 获取页面内容容器
            const pageContent = document.getElementById('page-content');
            
            // 插入页面内容
            pageContent.innerHTML = `
                <!-- 仪表盘区域 -->
                <div class="gauge-container">
                    <div class="gauge-card">
                        <canvas id="powerGauge" class="gauge-chart"></canvas>
                        <div style="font-weight: 600; color: #333;">总功率</div>
                        <div style="font-size: 12px; color: #8c8c8c;">kW</div>
                    </div>
                    <div class="gauge-card">
                        <canvas id="voltageGauge" class="gauge-chart"></canvas>
                        <div style="font-weight: 600; color: #333;">电压</div>
                        <div style="font-size: 12px; color: #8c8c8c;">V</div>
                    </div>
                    <div class="gauge-card">
                        <canvas id="currentGauge" class="gauge-chart"></canvas>
                        <div style="font-weight: 600; color: #333;">电流</div>
                        <div style="font-size: 12px; color: #8c8c8c;">A</div>
                    </div>
                    <div class="gauge-card">
                        <canvas id="frequencyGauge" class="gauge-chart"></canvas>
                        <div style="font-weight: 600; color: #333;">频率</div>
                        <div style="font-size: 12px; color: #8c8c8c;">Hz</div>
                    </div>
                </div>

                <!-- 实时监控图表 -->
                <div class="monitoring-grid">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">实时功率曲线</h3>
                            <div class="auto-refresh">
                                <i class="fas fa-circle blink"></i>
                                <span>实时更新</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div style="height: 300px; position: relative;">
                                <canvas id="powerTrendChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">多能源监控</h3>
                        </div>
                        <div class="card-body">
                            <div style="height: 300px; position: relative;">
                                <canvas id="multiEnergyChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 设备监控仪表 -->
                <div class="meter-grid">
                    <div class="meter-card">
                        <div class="meter-header">
                            <div class="meter-title">
                                <i class="fas fa-snowflake" style="color: #1890ff; margin-right: 8px;"></i>
                                空调系统
                            </div>
                            <div class="meter-status status-success">正常运行</div>
                        </div>
                        <div class="meter-readings">
                            <div class="reading-item">
                                <div class="reading-value">120.5</div>
                                <div class="reading-label">功率 (kW)</div>
                            </div>
                            <div class="reading-item">
                                <div class="reading-value">22.5</div>
                                <div class="reading-label">温度 (°C)</div>
                            </div>
                            <div class="reading-item">
                                <div class="reading-value">65</div>
                                <div class="reading-label">湿度 (%)</div>
                            </div>
                            <div class="reading-item">
                                <div class="reading-value">0.92</div>
                                <div class="reading-label">功率因数</div>
                            </div>
                        </div>
                    </div>

                    <div class="meter-card">
                        <div class="meter-header">
                            <div class="meter-title">
                                <i class="fas fa-lightbulb" style="color: #faad14; margin-right: 8px;"></i>
                                照明系统
                            </div>
                            <div class="meter-status status-success">正常运行</div>
                        </div>
                        <div class="meter-readings">
                            <div class="reading-item">
                                <div class="reading-value">45.2</div>
                                <div class="reading-label">功率 (kW)</div>
                            </div>
                            <div class="reading-item">
                                <div class="reading-value">220</div>
                                <div class="reading-label">电压 (V)</div>
                            </div>
                            <div class="reading-item">
                                <div class="reading-value">205</div>
                                <div class="reading-label">电流 (A)</div>
                            </div>
                            <div class="reading-item">
                                <div class="reading-value">0.95</div>
                                <div class="reading-label">功率因数</div>
                            </div>
                        </div>
                    </div>

                    <div class="meter-card">
                        <div class="meter-header">
                            <div class="meter-title">
                                <i class="fas fa-tint" style="color: #52c41a; margin-right: 8px;"></i>
                                供水系统
                            </div>
                            <div class="meter-status status-success">正常运行</div>
                        </div>
                        <div class="meter-readings">
                            <div class="reading-item">
                                <div class="reading-value">32.1</div>
                                <div class="reading-label">功率 (kW)</div>
                            </div>
                            <div class="reading-item">
                                <div class="reading-value">2.5</div>
                                <div class="reading-label">水压 (MPa)</div>
                            </div>
                            <div class="reading-item">
                                <div class="reading-value">15.8</div>
                                <div class="reading-label">流量 (m³/h)</div>
                            </div>
                            <div class="reading-item">
                                <div class="reading-value">18.5</div>
                                <div class="reading-label">温度 (°C)</div>
                            </div>
                        </div>
                    </div>

                    <div class="meter-card">
                        <div class="meter-header">
                            <div class="meter-title">
                                <i class="fas fa-fire" style="color: #f5222d; margin-right: 8px;"></i>
                                燃气系统
                            </div>
                            <div class="meter-status status-success">正常运行</div>
                        </div>
                        <div class="meter-readings">
                            <div class="reading-item">
                                <div class="reading-value">8.7</div>
                                <div class="reading-label">流量 (m³/h)</div>
                            </div>
                            <div class="reading-item">
                                <div class="reading-value">0.25</div>
                                <div class="reading-label">压力 (MPa)</div>
                            </div>
                            <div class="reading-item">
                                <div class="reading-value">15.2</div>
                                <div class="reading-label">温度 (°C)</div>
                            </div>
                            <div class="reading-item">
                                <div class="reading-value">正常</div>
                                <div class="reading-label">泄漏检测</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 实时数据表格 -->
                <div class="real-time-table">
                    <div class="table-header">
                        <h3 style="margin: 0; font-size: 16px; font-weight: 600;">实时数据监控</h3>
                        <div class="auto-refresh">
                            <i class="fas fa-sync-alt"></i>
                            <span>每5秒更新</span>
                        </div>
                    </div>
                    <div style="overflow-x: auto;">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>设备名称</th>
                                    <th>功率 (kW)</th>
                                    <th>电压 (V)</th>
                                    <th>电流 (A)</th>
                                    <th>功率因数</th>
                                    <th>状态</th>
                                    <th>更新时间</th>
                                </tr>
                            </thead>
                            <tbody id="realTimeTableBody">
                                <!-- 数据将通过JavaScript动态插入 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
            
            // 初始化监测页面
            initializeMonitoring();
        });
        
        // 初始化监测页面
        function initializeMonitoring() {
            // 创建仪表盘
            createGaugeChart('powerGauge', 287.5, 500, '287.5 kW');
            createGaugeChart('voltageGauge', 220, 250, '220 V');
            createGaugeChart('currentGauge', 1305, 2000, '1305 A');
            createGaugeChart('frequencyGauge', 50, 60, '50 Hz');
            
            // 创建实时功率曲线
            const powerTrendData = {
                labels: Array.from({length: 20}, (_, i) => `${i}s`),
                datasets: [{
                    label: '实时功率',
                    data: Array.from({length: 20}, () => Math.random() * 100 + 250),
                    borderColor: '#1890ff',
                    backgroundColor: 'rgba(24, 144, 255, 0.1)',
                    fill: true,
                    tension: 0.4,
                    pointRadius: 0
                }]
            };
            
            window.powerTrendChart = createLineChart('powerTrendChart', powerTrendData, {
                animation: false,
                scales: {
                    x: {
                        display: false
                    }
                }
            });
            
            // 创建多能源监控图表
            const multiEnergyData = {
                labels: Array.from({length: 10}, (_, i) => `${i}min`),
                datasets: [{
                    label: '电力 (kW)',
                    data: Array.from({length: 10}, () => Math.random() * 50 + 200),
                    borderColor: '#1890ff',
                    backgroundColor: 'rgba(24, 144, 255, 0.1)',
                    yAxisID: 'y'
                }, {
                    label: '水 (m³/h)',
                    data: Array.from({length: 10}, () => Math.random() * 10 + 15),
                    borderColor: '#52c41a',
                    backgroundColor: 'rgba(82, 196, 26, 0.1)',
                    yAxisID: 'y1'
                }, {
                    label: '气 (m³/h)',
                    data: Array.from({length: 10}, () => Math.random() * 5 + 8),
                    borderColor: '#faad14',
                    backgroundColor: 'rgba(250, 173, 20, 0.1)',
                    yAxisID: 'y1'
                }]
            };
            
            createLineChart('multiEnergyChart', multiEnergyData, {
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            });
            
            // 初始化实时数据表格
            updateRealTimeTable();
            
            // 启动实时更新
            startRealTimeMonitoring();
        }
        
        // 更新实时数据表格
        function updateRealTimeTable() {
            const tableBody = document.getElementById('realTimeTableBody');
            const devices = [
                { name: '空调系统', power: 120.5, voltage: 220, current: 548, pf: 0.92, status: 'normal' },
                { name: '照明系统', power: 45.2, voltage: 220, current: 205, pf: 0.95, status: 'normal' },
                { name: '电梯系统', power: 89.7, voltage: 380, current: 236, pf: 0.88, status: 'warning' },
                { name: '供水系统', power: 32.1, voltage: 380, current: 84, pf: 0.91, status: 'normal' }
            ];
            
            let html = '';
            devices.forEach(device => {
                const statusClass = device.status === 'normal' ? 'status-success' : 
                                  device.status === 'warning' ? 'status-warning' : 'status-danger';
                const statusText = device.status === 'normal' ? '正常' : 
                                 device.status === 'warning' ? '告警' : '故障';
                
                // 添加随机波动
                const power = device.power + (Math.random() - 0.5) * 10;
                const voltage = device.voltage + (Math.random() - 0.5) * 5;
                const current = device.current + (Math.random() - 0.5) * 20;
                const pf = device.pf + (Math.random() - 0.5) * 0.1;
                
                html += `
                    <tr>
                        <td>${device.name}</td>
                        <td>${power.toFixed(1)}</td>
                        <td>${voltage.toFixed(0)}</td>
                        <td>${current.toFixed(0)}</td>
                        <td>${pf.toFixed(2)}</td>
                        <td><span class="status-tag ${statusClass}">${statusText}</span></td>
                        <td>${formatDateTime(new Date())}</td>
                    </tr>
                `;
            });
            
            tableBody.innerHTML = html;
        }
        
        // 启动实时监控
        function startRealTimeMonitoring() {
            // 每5秒更新一次数据
            setInterval(() => {
                // 更新仪表盘
                const powerValue = 287.5 + (Math.random() - 0.5) * 50;
                const voltageValue = 220 + (Math.random() - 0.5) * 10;
                const currentValue = 1305 + (Math.random() - 0.5) * 100;
                const frequencyValue = 50 + (Math.random() - 0.5) * 0.5;

                createGaugeChart('powerGauge', powerValue, 500, `${powerValue.toFixed(1)} kW`);
                createGaugeChart('voltageGauge', voltageValue, 250, `${voltageValue.toFixed(0)} V`);
                createGaugeChart('currentGauge', currentValue, 2000, `${currentValue.toFixed(0)} A`);
                createGaugeChart('frequencyGauge', frequencyValue, 60, `${frequencyValue.toFixed(1)} Hz`);

                // 更新功率曲线
                const chart = window.powerTrendChart;
                chart.data.datasets[0].data.shift();
                chart.data.datasets[0].data.push(Math.random() * 100 + 250);
                chart.update('none');

                // 更新多能源图表
                const multiChart = window.multiEnergyChart;
                if (multiChart) {
                    multiChart.data.datasets.forEach((dataset, index) => {
                        dataset.data.shift();
                        if (index === 0) {
                            dataset.data.push(Math.random() * 50 + 200);
                        } else if (index === 1) {
                            dataset.data.push(Math.random() * 10 + 15);
                        } else {
                            dataset.data.push(Math.random() * 5 + 8);
                        }
                    });
                    multiChart.update('none');
                }

                // 更新数据表格
                updateRealTimeTable();
            }, 5000);
        }
    </script>
    
    <!-- 引入公共脚本 -->
    <script src="assets/js/common.js"></script>
    <script src="assets/js/charts.js"></script>
</body>
</html>
