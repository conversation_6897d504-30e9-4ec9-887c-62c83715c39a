<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧能源管理平台 - 首页总览</title>
    
    <!-- 外部CSS库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    
    <!-- 外部JS库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        /* 首页特定样式 */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-bottom: 24px;
        }
        
        .chart-container {
            height: 300px;
            position: relative;
        }
        
        .mini-chart {
            height: 60px;
            width: 100%;
        }
        
        .overview-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .quick-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            margin-bottom: 24px;
        }
        
        .action-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            background: white;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s;
        }
        
        .action-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
            box-shadow: 0 2px 8px rgba(24,144,255,0.2);
        }
        
        .alert-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border-left: 4px solid #faad14;
            background: #fffbe6;
            border-radius: 4px;
            margin-bottom: 8px;
        }
        
        .alert-item.critical {
            border-left-color: #f5222d;
            background: #fff2f0;
        }
        
        .device-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 16px;
        }
        
        .device-card {
            background: white;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .device-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-online { background: #52c41a; }
        .status-offline { background: #f5222d; }
        .status-warning { background: #faad14; }

        /* 卡片容器样式 */
        .card-container {
            min-height: 140px;
            display: flex;
            align-items: stretch;
        }

        /* 响应式设计优化 */
        @media (max-width: 768px) {
            .overview-stats {
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 12px;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .quick-actions {
                justify-content: center;
            }

            .action-btn {
                flex: 1;
                min-width: 120px;
            }
        }

        @media (max-width: 480px) {
            .overview-stats {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .device-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <script>
        // 页面加载完成后初始化布局
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化页面布局
            initializeLayout('首页总览', []);
            
            // 获取页面内容容器
            const pageContent = document.getElementById('page-content');
            
            // 插入页面内容
            pageContent.innerHTML = `
                <!-- 快速操作按钮 -->
                <div class="quick-actions">
                    <a href="monitoring.html" class="action-btn">
                        <i class="fas fa-eye"></i>
                        <span>实时监测</span>
                    </a>
                    <a href="alerts.html" class="action-btn">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>告警管理</span>
                    </a>
                    <a href="reports.html" class="action-btn">
                        <i class="fas fa-file-alt"></i>
                        <span>生成报表</span>
                    </a>
                    <a href="control.html" class="action-btn">
                        <i class="fas fa-sliders-h"></i>
                        <span>控制策略</span>
                    </a>
                </div>

                <!-- 概览统计 -->
                <div class="overview-stats">
                    <div id="power-card" class="card-container"></div>
                    <div id="water-card" class="card-container"></div>
                    <div id="gas-card" class="card-container"></div>
                    <div id="efficiency-card" class="card-container"></div>
                </div>

                <!-- 主要图表区域 -->
                <div class="dashboard-grid">
                    <!-- 能耗趋势图 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">今日能耗趋势</h3>
                            <div>
                                <button class="btn btn-primary" onclick="refreshTrendChart()">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="trendChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- 能源分布饼图 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">能源消耗分布</h3>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="distributionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 设备状态和告警信息 -->
                <div class="dashboard-grid">
                    <!-- 设备状态 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">设备状态监控</h3>
                            <span class="status-tag status-success">全部正常</span>
                        </div>
                        <div class="card-body">
                            <div class="device-grid">
                                <div class="device-card">
                                    <div class="device-status status-online"></div>
                                    <div>空调系统</div>
                                    <div style="font-size: 12px; color: #8c8c8c; margin-top: 4px;">120.5 kW</div>
                                </div>
                                <div class="device-card">
                                    <div class="device-status status-online"></div>
                                    <div>照明系统</div>
                                    <div style="font-size: 12px; color: #8c8c8c; margin-top: 4px;">45.2 kW</div>
                                </div>
                                <div class="device-card">
                                    <div class="device-status status-warning"></div>
                                    <div>电梯系统</div>
                                    <div style="font-size: 12px; color: #8c8c8c; margin-top: 4px;">89.7 kW</div>
                                </div>
                                <div class="device-card">
                                    <div class="device-status status-online"></div>
                                    <div>供水系统</div>
                                    <div style="font-size: 12px; color: #8c8c8c; margin-top: 4px;">32.1 kW</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 最新告警 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">最新告警</h3>
                            <a href="alerts.html" class="btn btn-primary">查看全部</a>
                        </div>
                        <div class="card-body">
                            <div class="alert-item">
                                <i class="fas fa-exclamation-triangle" style="color: #faad14;"></i>
                                <div>
                                    <div style="font-weight: 500;">电梯系统功率异常</div>
                                    <div style="font-size: 12px; color: #8c8c8c;">2分钟前</div>
                                </div>
                            </div>
                            <div class="alert-item critical">
                                <i class="fas fa-exclamation-circle" style="color: #f5222d;"></i>
                                <div>
                                    <div style="font-weight: 500;">3楼空调温度传感器故障</div>
                                    <div style="font-size: 12px; color: #8c8c8c;">15分钟前</div>
                                </div>
                            </div>
                            <div class="alert-item">
                                <i class="fas fa-info-circle" style="color: #1890ff;"></i>
                                <div>
                                    <div style="font-weight: 500;">用电量接近峰值</div>
                                    <div style="font-size: 12px; color: #8c8c8c;">1小时前</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // 初始化图表和数据
            initializeDashboard();
        });
        
        // 初始化仪表盘
        function initializeDashboard() {
            // 创建数据卡片
            createDataCard('power-card', '总用电量', 1247.8, 'kWh', 2.3, 'fas fa-bolt');
            createDataCard('water-card', '总用水量', 89.5, 'm³', -1.2, 'fas fa-tint');
            createDataCard('gas-card', '总用气量', 34.7, 'm³', 0.8, 'fas fa-fire');
            createDataCard('efficiency-card', '能效比', 85.6, '%', 1.5, 'fas fa-leaf');
            
            // 创建趋势图
            const trendData = {
                labels: Array.from({length: 24}, (_, i) => `${i.toString().padStart(2, '0')}:00`),
                datasets: [{
                    label: '用电量 (kW)',
                    data: Array.from({length: 24}, () => Math.random() * 200 + 300),
                    borderColor: '#1890ff',
                    backgroundColor: 'rgba(24, 144, 255, 0.1)',
                    fill: true,
                    tension: 0.4
                }, {
                    label: '用水量 (m³/h)',
                    data: Array.from({length: 24}, () => Math.random() * 20 + 10),
                    borderColor: '#52c41a',
                    backgroundColor: 'rgba(82, 196, 26, 0.1)',
                    fill: true,
                    tension: 0.4
                }]
            };
            
            window.trendChart = createLineChart('trendChart', trendData);
            
            // 创建分布饼图
            const distributionData = {
                labels: ['空调系统', '照明系统', '电梯系统', '供水系统', '其他设备'],
                datasets: [{
                    data: [35, 20, 15, 10, 20],
                    backgroundColor: ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1']
                }]
            };
            
            createPieChart('distributionChart', distributionData);
            
            // 启动实时数据更新
            startRealTimeUpdate();
        }
        
        // 刷新趋势图
        function refreshTrendChart() {
            const newData = {
                labels: Array.from({length: 24}, (_, i) => `${i.toString().padStart(2, '0')}:00`),
                datasets: [{
                    label: '用电量 (kW)',
                    data: Array.from({length: 24}, () => Math.random() * 200 + 300),
                    borderColor: '#1890ff',
                    backgroundColor: 'rgba(24, 144, 255, 0.1)',
                    fill: true,
                    tension: 0.4
                }, {
                    label: '用水量 (m³/h)',
                    data: Array.from({length: 24}, () => Math.random() * 20 + 10),
                    borderColor: '#52c41a',
                    backgroundColor: 'rgba(82, 196, 26, 0.1)',
                    fill: true,
                    tension: 0.4
                }]
            };
            
            updateChartData(window.trendChart, newData);
            showNotification('数据已更新', 'success');
        }
        
        // 启动实时数据更新
        function startRealTimeUpdate() {
            setInterval(() => {
                // 更新数据卡片
                const mockData = generateMockData();
                createDataCard('power-card', '总用电量', mockData.realTimeData.totalPower, 'kWh', Math.random() * 4 - 2, 'fas fa-bolt');
                createDataCard('water-card', '总用水量', mockData.realTimeData.waterUsage, 'm³', Math.random() * 4 - 2, 'fas fa-tint');
                createDataCard('gas-card', '总用气量', mockData.realTimeData.gasUsage, 'm³', Math.random() * 4 - 2, 'fas fa-fire');
                createDataCard('efficiency-card', '能效比', 85.6 + Math.random() * 10 - 5, '%', Math.random() * 4 - 2, 'fas fa-leaf');
            }, 30000); // 每30秒更新一次
        }
    </script>
    
    <!-- 引入公共脚本 -->
    <script src="assets/js/common.js"></script>
    <script src="assets/js/charts.js"></script>
</body>
</html>
