<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>储能管理 - 智慧能源管理平台</title>
    <link rel="stylesheet" href="assets/css/common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="assets/js/charts.js"></script>
    <style>
        /* 储能管理专用样式 */
        .storage-container {
            padding: 24px;
            background: #f5f5f5;
            min-height: 100vh;
        }
        
        .storage-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 24px;
            border-radius: 12px;
            margin-bottom: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .storage-title {
            font-size: 28px;
            font-weight: 600;
            margin: 0 0 8px 0;
        }
        
        .storage-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin: 0;
        }
        
        /* 标签页导航 */
        .tab-navigation {
            display: flex;
            background: white;
            border-radius: 8px;
            padding: 4px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow-x: auto;
        }
        
        .tab-button {
            flex: 1;
            padding: 12px 20px;
            border: none;
            background: transparent;
            color: #666;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            min-width: 120px;
        }
        
        .tab-button:hover {
            background: #f0f0f0;
            color: #333;
        }
        
        .tab-button.active {
            background: #1890ff;
            color: white;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
        }
        
        /* 内容区域 */
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        /* 储能概览卡片 */
        .storage-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .storage-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            border-left: 4px solid #1890ff;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .storage-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.12);
        }
        
        .storage-card.warning {
            border-left-color: #faad14;
        }
        
        .storage-card.success {
            border-left-color: #52c41a;
        }
        
        .storage-card.danger {
            border-left-color: #f5222d;
        }
        
        .storage-card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }
        
        .storage-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background: #1890ff;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }
        
        .storage-value {
            font-size: 32px;
            font-weight: 700;
            color: #333;
            margin: 8px 0;
        }
        
        .storage-unit {
            font-size: 16px;
            font-weight: 400;
            color: #666;
        }
        
        .storage-trend {
            font-size: 14px;
            color: #666;
            margin-top: 8px;
        }
        
        /* 状态标签 */
        .status-tag {
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-success {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        
        .status-warning {
            background: #fffbe6;
            color: #faad14;
            border: 1px solid #ffe58f;
        }
        
        .status-danger {
            background: #fff2f0;
            color: #f5222d;
            border: 1px solid #ffccc7;
        }
        
        .status-info {
            background: #e6f7ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }
        
        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 24px;
        }
        
        .card-header {
            padding: 20px 24px 0;
            border-bottom: 1px solid #f0f0f0;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin: 0 0 20px 0;
        }
        
        .card-body {
            padding: 0 24px 24px;
        }
        
        /* 按钮样式 */
        .btn {
            padding: 8px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: white;
            color: #333;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }
        
        .btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        
        .btn-primary {
            background: #1890ff;
            border-color: #1890ff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #40a9ff;
            border-color: #40a9ff;
        }
        
        .btn-success {
            background: #52c41a;
            border-color: #52c41a;
            color: white;
        }
        
        .btn-warning {
            background: #faad14;
            border-color: #faad14;
            color: white;
        }
        
        .btn-danger {
            background: #f5222d;
            border-color: #f5222d;
            color: white;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .storage-container {
                padding: 16px;
            }
            
            .storage-overview {
                grid-template-columns: 1fr;
            }
            
            .tab-navigation {
                flex-direction: column;
            }
            
            .tab-button {
                min-width: auto;
            }
            
            .storage-header {
                padding: 20px;
            }
            
            .storage-title {
                font-size: 24px;
            }
        }
        
        @media (max-width: 480px) {
            .storage-container {
                padding: 12px;
            }
            
            .card-header,
            .card-body {
                padding-left: 16px;
                padding-right: 16px;
            }
            
            .storage-value {
                font-size: 28px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 导航栏 -->
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-brand">
                    <i class="fas fa-bolt"></i>
                    <span>智慧能源管理平台</span>
                </div>
                <div class="nav-menu" id="navMenu">
                    <!-- 菜单项将通过JavaScript动态生成 -->
                </div>
                <div class="nav-toggle" id="navToggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </nav>

        <!-- 主要内容区域 -->
        <main class="storage-container">
            <!-- 页面标题 -->
            <div class="storage-header">
                <h1 class="storage-title">
                    <i class="fas fa-battery-three-quarters"></i>
                    储能管理
                </h1>
                <p class="storage-subtitle">实时监控储能系统运行状态，智能调度充放电策略，优化能源配置</p>
            </div>

            <!-- 标签页导航 -->
            <div class="tab-navigation">
                <button class="tab-button active" onclick="switchTab('overview')">
                    <i class="fas fa-tachometer-alt"></i>
                    系统概览
                </button>
                <button class="tab-button" onclick="switchTab('realtime')">
                    <i class="fas fa-chart-line"></i>
                    实时监测
                </button>
                <button class="tab-button" onclick="switchTab('strategy')">
                    <i class="fas fa-cogs"></i>
                    策略调度
                </button>
                <button class="tab-button" onclick="switchTab('flowboard')">
                    <i class="fas fa-project-diagram"></i>
                    能源流向
                </button>
                <button class="tab-button" onclick="switchTab('optimization')">
                    <i class="fas fa-brain"></i>
                    AI优化
                </button>
            </div>

            <!-- 系统概览标签页 -->
            <div id="overview-content" class="tab-content active">
                <!-- 储能系统概览 -->
                <div class="storage-overview">
                    <div class="storage-card">
                        <div class="storage-card-header">
                            <div class="storage-icon">
                                <i class="fas fa-battery-three-quarters"></i>
                            </div>
                            <span class="status-tag status-success">正常</span>
                        </div>
                        <div class="storage-value">85.6 <span class="storage-unit">%</span></div>
                        <div class="storage-trend">当前电池容量</div>
                    </div>
                    
                    <div class="storage-card warning">
                        <div class="storage-card-header">
                            <div class="storage-icon" style="background: #faad14;">
                                <i class="fas fa-bolt"></i>
                            </div>
                            <span class="status-tag status-warning">充电中</span>
                        </div>
                        <div class="storage-value">125.8 <span class="storage-unit">kW</span></div>
                        <div class="storage-trend">当前充电功率</div>
                    </div>
                    
                    <div class="storage-card success">
                        <div class="storage-card-header">
                            <div class="storage-icon" style="background: #52c41a;">
                                <i class="fas fa-leaf"></i>
                            </div>
                            <span class="status-tag status-success">高效</span>
                        </div>
                        <div class="storage-value">94.2 <span class="storage-unit">%</span></div>
                        <div class="storage-trend">系统效率</div>
                    </div>
                    
                    <div class="storage-card">
                        <div class="storage-card-header">
                            <div class="storage-icon" style="background: #722ed1;">
                                <i class="fas fa-coins"></i>
                            </div>
                            <span class="status-tag status-info">今日</span>
                        </div>
                        <div class="storage-value">2,856 <span class="storage-unit">元</span></div>
                        <div class="storage-trend">节省电费</div>
                    </div>
                </div>
            </div>

            <!-- 其他标签页内容将通过JavaScript动态加载 -->
            <div id="realtime-content" class="tab-content"></div>
            <div id="strategy-content" class="tab-content"></div>
            <div id="flowboard-content" class="tab-content"></div>
            <div id="optimization-content" class="tab-content"></div>
        </main>
    </div>

    <!-- 引入通用JavaScript -->
    <script src="assets/js/common.js"></script>
    <script>
        // 储能管理页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化通用布局
            initializeLayout();
            
            // 设置当前页面为活跃状态
            setActiveMenuItem('storage');
            
            // 加载默认内容
            loadOverviewContent();
        });
        
        // 切换标签页
        function switchTab(tabName) {
            // 移除所有活跃状态
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            
            // 设置当前标签页为活跃状态
            event.target.classList.add('active');
            document.getElementById(tabName + '-content').classList.add('active');
            
            // 根据标签页加载对应内容
            switch(tabName) {
                case 'overview':
                    loadOverviewContent();
                    break;
                case 'realtime':
                    loadRealtimeContent();
                    break;
                case 'strategy':
                    loadStrategyContent();
                    break;
                case 'flowboard':
                    loadFlowboardContent();
                    break;
                case 'optimization':
                    loadOptimizationContent();
                    break;
            }
        }
        
        // 加载概览内容
        function loadOverviewContent() {
            // 概览内容已在HTML中定义，这里可以添加动态数据更新
            console.log('储能系统概览已加载');
        }

        // 加载实时监测内容
        function loadRealtimeContent() {
            const content = document.getElementById('realtime-content');
            content.innerHTML = `
                <!-- 实时数据监测 -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 24px;">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">储能系统实时参数</h3>
                            <button class="btn btn-primary" onclick="refreshRealtimeData()">
                                <i class="fas fa-sync-alt"></i>
                                刷新数据
                            </button>
                        </div>
                        <div class="card-body">
                            <div id="storageParametersTable">
                                <!-- 实时参数表格将通过JavaScript动态插入 -->
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">电网交互数据</h3>
                        </div>
                        <div class="card-body">
                            <div id="gridInteractionData">
                                <!-- 电网交互数据将通过JavaScript动态插入 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 实时图表 -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 24px;">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">功率变化趋势</h3>
                        </div>
                        <div class="card-body">
                            <div style="height: 300px; position: relative;">
                                <canvas id="powerTrendChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">电池状态监控</h3>
                        </div>
                        <div class="card-body">
                            <div style="height: 300px; position: relative;">
                                <canvas id="batteryStatusChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 光伏系统集成数据 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-solar-panel" style="margin-right: 8px;"></i>
                            光伏系统集成数据
                        </h3>
                        <div>
                            <button class="btn btn-primary" onclick="refreshSolarData()">
                                <i class="fas fa-sync-alt"></i>
                                刷新
                            </button>
                            <button class="btn btn-warning" onclick="exportRealtimeData()">
                                <i class="fas fa-download"></i>
                                导出数据
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="solarIntegrationData">
                            <!-- 光伏集成数据将通过JavaScript动态插入 -->
                        </div>
                    </div>
                </div>
            `;

            // 初始化实时监测图表和数据
            initializePowerTrendChart();
            initializeBatteryStatusChart();
            loadStorageParameters();
            loadGridInteractionData();
            loadSolarIntegrationData();
        }

        // 初始化功率趋势图表
        function initializePowerTrendChart() {
            const ctx = document.getElementById('powerTrendChart');
            if (!ctx) return;

            const data = {
                labels: generateTimeLabels(24),
                datasets: [
                    {
                        label: '充电功率 (kW)',
                        data: generatePowerData('charge'),
                        borderColor: '#52c41a',
                        backgroundColor: 'rgba(82, 196, 26, 0.1)',
                        fill: true,
                        tension: 0.4
                    },
                    {
                        label: '放电功率 (kW)',
                        data: generatePowerData('discharge'),
                        borderColor: '#f5222d',
                        backgroundColor: 'rgba(245, 34, 45, 0.1)',
                        fill: true,
                        tension: 0.4
                    }
                ]
            };

            window.powerTrendChart = createLineChart('powerTrendChart', data);
        }

        // 初始化电池状态图表
        function initializeBatteryStatusChart() {
            const ctx = document.getElementById('batteryStatusChart');
            if (!ctx) return;

            const data = {
                labels: ['电池组1', '电池组2', '电池组3', '电池组4', '电池组5'],
                datasets: [{
                    label: '电池容量 (%)',
                    data: [85.6, 87.2, 83.4, 89.1, 86.8],
                    backgroundColor: [
                        '#1890ff',
                        '#52c41a',
                        '#faad14',
                        '#722ed1',
                        '#13c2c2'
                    ]
                }]
            };

            createBarChart('batteryStatusChart', data);
        }

        // 生成时间标签
        function generateTimeLabels(hours) {
            const labels = [];
            for (let i = 0; i < hours; i++) {
                labels.push(`${i.toString().padStart(2, '0')}:00`);
            }
            return labels;
        }

        // 生成功率数据
        function generatePowerData(type) {
            const data = [];
            for (let i = 0; i < 24; i++) {
                if (type === 'charge') {
                    // 充电功率：夜间和中午较高
                    if (i >= 0 && i < 6) {
                        data.push(Math.random() * 100 + 80); // 夜间充电
                    } else if (i >= 11 && i < 15) {
                        data.push(Math.random() * 120 + 100); // 中午光伏充电
                    } else {
                        data.push(Math.random() * 40 + 10);
                    }
                } else {
                    // 放电功率：早晚用电高峰
                    if ((i >= 7 && i < 10) || (i >= 18 && i < 22)) {
                        data.push(Math.random() * 150 + 100); // 用电高峰放电
                    } else {
                        data.push(Math.random() * 50 + 20);
                    }
                }
            }
            return data;
        }

        // 加载储能系统参数
        function loadStorageParameters() {
            const container = document.getElementById('storageParametersTable');
            if (!container) return;

            const parameters = [
                { name: '总容量', value: '500.0', unit: 'kWh', status: '正常' },
                { name: '可用容量', value: '428.0', unit: 'kWh', status: '正常' },
                { name: '当前电压', value: '385.6', unit: 'V', status: '正常' },
                { name: '当前电流', value: '326.4', unit: 'A', status: '正常' },
                { name: '充电功率', value: '125.8', unit: 'kW', status: '充电中' },
                { name: '放电功率', value: '0.0', unit: 'kW', status: '待机' },
                { name: '系统温度', value: '28.5', unit: '°C', status: '正常' },
                { name: '循环次数', value: '1,247', unit: '次', status: '良好' }
            ];

            container.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                    ${parameters.map(param => `
                        <div style="background: #f8f9fa; padding: 16px; border-radius: 8px; border-left: 4px solid ${getParameterColor(param.status)};">
                            <div style="font-size: 14px; color: #666; margin-bottom: 4px;">${param.name}</div>
                            <div style="font-size: 20px; font-weight: 600; color: #333; margin-bottom: 4px;">
                                ${param.value} <span style="font-size: 14px; font-weight: 400; color: #666;">${param.unit}</span>
                            </div>
                            <div style="font-size: 12px; color: ${getParameterColor(param.status)};">${param.status}</div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        // 获取参数状态颜色
        function getParameterColor(status) {
            switch(status) {
                case '正常': return '#52c41a';
                case '充电中': return '#faad14';
                case '放电中': return '#f5222d';
                case '待机': return '#1890ff';
                case '良好': return '#52c41a';
                default: return '#666';
            }
        }

        // 加载电网交互数据
        function loadGridInteractionData() {
            const container = document.getElementById('gridInteractionData');
            if (!container) return;

            const gridData = [
                { name: '电网频率', value: '50.02', unit: 'Hz', trend: '稳定' },
                { name: '电网电压', value: '220.5', unit: 'V', trend: '正常' },
                { name: '向电网输出', value: '0.0', unit: 'kW', trend: '待机' },
                { name: '从电网充电', value: '125.8', unit: 'kW', trend: '充电中' },
                { name: '功率因数', value: '0.98', unit: '', trend: '优秀' },
                { name: '谐波失真', value: '2.1', unit: '%', trend: '良好' }
            ];

            container.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); gap: 12px;">
                    ${gridData.map(item => `
                        <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; text-align: center;">
                            <div style="font-size: 12px; color: #666; margin-bottom: 4px;">${item.name}</div>
                            <div style="font-size: 18px; font-weight: 600; color: #333; margin-bottom: 4px;">
                                ${item.value} <span style="font-size: 12px; font-weight: 400;">${item.unit}</span>
                            </div>
                            <div style="font-size: 11px; color: #52c41a;">${item.trend}</div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        // 加载光伏集成数据
        function loadSolarIntegrationData() {
            const container = document.getElementById('solarIntegrationData');
            if (!container) return;

            const solarData = [
                { name: '光伏发电功率', value: '285.6', unit: 'kW', status: '发电中' },
                { name: '光伏日发电量', value: '1,856', unit: 'kWh', status: '正常' },
                { name: '储能充电来源', value: '光伏+电网', unit: '', status: '混合' },
                { name: '光伏利用率', value: '87.3', unit: '%', status: '高效' },
                { name: '余电上网', value: '159.8', unit: 'kW', status: '上网中' },
                { name: '储能占比', value: '44.1', unit: '%', status: '适中' }
            ];

            container.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                    ${solarData.map(item => `
                        <div style="background: #f0f9ff; padding: 16px; border-radius: 8px; border: 1px solid #e6f7ff;">
                            <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;">
                                <span style="font-size: 14px; color: #666;">${item.name}</span>
                                <span style="font-size: 12px; padding: 2px 8px; background: #e6f7ff; color: #1890ff; border-radius: 12px;">${item.status}</span>
                            </div>
                            <div style="font-size: 20px; font-weight: 600; color: #1890ff;">
                                ${item.value} <span style="font-size: 14px; font-weight: 400; color: #666;">${item.unit}</span>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        // 加载策略调度内容
        function loadStrategyContent() {
            const content = document.getElementById('strategy-content');
            content.innerHTML = `
                <!-- 充放电策略控制 -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 24px;">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-cogs" style="margin-right: 8px;"></i>
                                充放电策略设置
                            </h3>
                        </div>
                        <div class="card-body">
                            <div style="margin-bottom: 20px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: 500;">运行模式：</label>
                                <select class="form-control" id="operationMode" onchange="updateOperationMode()">
                                    <option value="auto">自动模式</option>
                                    <option value="manual">手动模式</option>
                                    <option value="peak-valley">峰谷电价模式</option>
                                    <option value="solar-priority">光伏优先模式</option>
                                </select>
                            </div>

                            <div style="margin-bottom: 20px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: 500;">充电功率限制：</label>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <input type="range" id="chargePowerLimit" min="0" max="200" value="150"
                                           style="flex: 1;" onchange="updateChargePowerLimit(this.value)">
                                    <span id="chargePowerValue" style="min-width: 60px; font-weight: 600;">150 kW</span>
                                </div>
                            </div>

                            <div style="margin-bottom: 20px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: 500;">放电功率限制：</label>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <input type="range" id="dischargePowerLimit" min="0" max="200" value="180"
                                           style="flex: 1;" onchange="updateDischargePowerLimit(this.value)">
                                    <span id="dischargePowerValue" style="min-width: 60px; font-weight: 600;">180 kW</span>
                                </div>
                            </div>

                            <div style="margin-bottom: 20px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: 500;">SOC控制范围：</label>
                                <div style="display: flex; gap: 12px;">
                                    <div style="flex: 1;">
                                        <label style="font-size: 12px; color: #666;">最低SOC (%)</label>
                                        <input type="number" class="form-control" value="20" min="10" max="50">
                                    </div>
                                    <div style="flex: 1;">
                                        <label style="font-size: 12px; color: #666;">最高SOC (%)</label>
                                        <input type="number" class="form-control" value="95" min="80" max="100">
                                    </div>
                                </div>
                            </div>

                            <div style="text-align: center;">
                                <button class="btn btn-primary" onclick="applyStrategy()" style="padding: 12px 32px;">
                                    <i class="fas fa-check"></i>
                                    应用策略
                                </button>
                                <button class="btn btn-warning" onclick="resetStrategy()" style="padding: 12px 32px; margin-left: 12px;">
                                    <i class="fas fa-undo"></i>
                                    重置
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-clock" style="margin-right: 8px;"></i>
                                时段策略配置
                            </h3>
                        </div>
                        <div class="card-body">
                            <div id="timeStrategyConfig">
                                <!-- 时段策略配置将通过JavaScript动态插入 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 峰谷电价策略 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-bar" style="margin-right: 8px;"></i>
                            峰谷电价策略
                        </h3>
                        <div>
                            <button class="btn btn-primary" onclick="refreshPeakValleyStrategy()">
                                <i class="fas fa-sync-alt"></i>
                                刷新策略
                            </button>
                            <button class="btn btn-success" onclick="optimizePeakValleyStrategy()">
                                <i class="fas fa-magic"></i>
                                智能优化
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px;">
                            <div>
                                <h4 style="margin-bottom: 16px;">电价时段设置</h4>
                                <div id="electricityPriceConfig">
                                    <!-- 电价配置将通过JavaScript动态插入 -->
                                </div>
                            </div>
                            <div>
                                <h4 style="margin-bottom: 16px;">策略执行计划</h4>
                                <div style="height: 300px; position: relative;">
                                    <canvas id="strategyPlanChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 远程调度控制 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-satellite-dish" style="margin-right: 8px;"></i>
                            远程调度控制
                        </h3>
                    </div>
                    <div class="card-body">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                            <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                                <div style="font-size: 48px; color: #52c41a; margin-bottom: 12px;">
                                    <i class="fas fa-play-circle"></i>
                                </div>
                                <h4 style="margin-bottom: 8px;">启动充电</h4>
                                <button class="btn btn-success" onclick="startCharging()">立即执行</button>
                            </div>

                            <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                                <div style="font-size: 48px; color: #f5222d; margin-bottom: 12px;">
                                    <i class="fas fa-stop-circle"></i>
                                </div>
                                <h4 style="margin-bottom: 8px;">停止充电</h4>
                                <button class="btn btn-danger" onclick="stopCharging()">立即执行</button>
                            </div>

                            <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                                <div style="font-size: 48px; color: #faad14; margin-bottom: 12px;">
                                    <i class="fas fa-bolt"></i>
                                </div>
                                <h4 style="margin-bottom: 8px;">启动放电</h4>
                                <button class="btn btn-warning" onclick="startDischarging()">立即执行</button>
                            </div>

                            <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                                <div style="font-size: 48px; color: #1890ff; margin-bottom: 12px;">
                                    <i class="fas fa-pause-circle"></i>
                                </div>
                                <h4 style="margin-bottom: 8px;">待机模式</h4>
                                <button class="btn btn-primary" onclick="setStandbyMode()">立即执行</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 初始化策略相关组件
            loadTimeStrategyConfig();
            loadElectricityPriceConfig();
            initializeStrategyPlanChart();
        }

        // 加载能源流向看板内容
        function loadFlowboardContent() {
            const content = document.getElementById('flowboard-content');
            content.innerHTML = `
                <!-- 能源流向图 -->
                <div class="card" style="margin-bottom: 24px;">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-project-diagram" style="margin-right: 8px;"></i>
                            能源流向实时监控
                        </h3>
                        <div>
                            <button class="btn btn-primary" onclick="refreshEnergyFlow()">
                                <i class="fas fa-sync-alt"></i>
                                刷新
                            </button>
                            <button class="btn btn-warning" onclick="toggleFlowAnimation()">
                                <i class="fas fa-play"></i>
                                动画
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="energyFlowDiagram" style="height: 400px; position: relative; background: #f8f9fa; border-radius: 8px;">
                            <!-- 能源流向图将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>

                <!-- 系统拓扑图 -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 24px;">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">输配电系统拓扑</h3>
                        </div>
                        <div class="card-body">
                            <div id="powerSystemTopology" style="height: 300px; position: relative;">
                                <!-- 输配电系统拓扑图 -->
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">能源流向统计</h3>
                        </div>
                        <div class="card-body">
                            <div style="height: 300px; position: relative;">
                                <canvas id="energyFlowChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 实时能源数据 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">实时能源数据</h3>
                    </div>
                    <div class="card-body">
                        <div id="realtimeEnergyData">
                            <!-- 实时能源数据表格 -->
                        </div>
                    </div>
                </div>
            `;

            // 初始化能源流向相关组件
            initializeEnergyFlowDiagram();
            initializePowerSystemTopology();
            initializeEnergyFlowChart();
            loadRealtimeEnergyData();
        }

        // 加载AI优化内容
        function loadOptimizationContent() {
            const content = document.getElementById('optimization-content');
            content.innerHTML = `
                <!-- AI分析概览 -->
                <div class="storage-overview" style="margin-bottom: 24px;">
                    <div class="storage-card success">
                        <div class="storage-card-header">
                            <div class="storage-icon" style="background: #52c41a;">
                                <i class="fas fa-brain"></i>
                            </div>
                            <span class="status-tag status-success">运行中</span>
                        </div>
                        <div class="storage-value">24/7 <span class="storage-unit"></span></div>
                        <div class="storage-trend">AI智能分析</div>
                    </div>

                    <div class="storage-card">
                        <div class="storage-card-header">
                            <div class="storage-icon" style="background: #1890ff;">
                                <i class="fas fa-lightbulb"></i>
                            </div>
                            <span class="status-tag status-info">今日</span>
                        </div>
                        <div class="storage-value">8 <span class="storage-unit">条</span></div>
                        <div class="storage-trend">节能建议</div>
                    </div>

                    <div class="storage-card warning">
                        <div class="storage-card-header">
                            <div class="storage-icon" style="background: #faad14;">
                                <i class="fas fa-coins"></i>
                            </div>
                            <span class="status-tag status-warning">预计</span>
                        </div>
                        <div class="storage-value">15.8 <span class="storage-unit">%</span></div>
                        <div class="storage-trend">节能潜力</div>
                    </div>

                    <div class="storage-card">
                        <div class="storage-card-header">
                            <div class="storage-icon" style="background: #722ed1;">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <span class="status-tag status-success">优化中</span>
                        </div>
                        <div class="storage-value">92.4 <span class="storage-unit">%</span></div>
                        <div class="storage-trend">策略执行率</div>
                    </div>
                </div>

                <!-- AI节能建议 -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 24px;">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-robot" style="margin-right: 8px;"></i>
                                AI节能建议
                            </h3>
                            <button class="btn btn-primary" onclick="generateNewSuggestions()">
                                <i class="fas fa-magic"></i>
                                生成建议
                            </button>
                        </div>
                        <div class="card-body">
                            <div id="energySuggestions">
                                <!-- AI节能建议列表 -->
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">峰谷电价优化</h3>
                        </div>
                        <div class="card-body">
                            <div style="height: 300px; position: relative;">
                                <canvas id="peakValleyOptimizationChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 历史数据分析 -->
                <div class="card" style="margin-bottom: 24px;">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-area" style="margin-right: 8px;"></i>
                            历史数据AI分析
                        </h3>
                        <div>
                            <button class="btn btn-primary" onclick="runAIAnalysis()">
                                <i class="fas fa-play"></i>
                                运行分析
                            </button>
                            <button class="btn btn-warning" onclick="exportAnalysisReport()">
                                <i class="fas fa-download"></i>
                                导出报告
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px;">
                            <div>
                                <h4 style="margin-bottom: 16px;">能效趋势分析</h4>
                                <div style="height: 250px; position: relative;">
                                    <canvas id="efficiencyTrendChart"></canvas>
                                </div>
                            </div>
                            <div>
                                <h4 style="margin-bottom: 16px;">成本优化分析</h4>
                                <div style="height: 250px; position: relative;">
                                    <canvas id="costOptimizationChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 智能调度策略 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-cogs" style="margin-right: 8px;"></i>
                            智能调度策略
                        </h3>
                    </div>
                    <div class="card-body">
                        <div id="intelligentSchedulingStrategy">
                            <!-- 智能调度策略配置 -->
                        </div>
                    </div>
                </div>
            `;

            // 初始化AI优化相关组件
            loadEnergySuggestions();
            initializePeakValleyOptimizationChart();
            initializeEfficiencyTrendChart();
            initializeCostOptimizationChart();
            loadIntelligentSchedulingStrategy();
        }

        // 时段策略配置
        function loadTimeStrategyConfig() {
            const container = document.getElementById('timeStrategyConfig');
            if (!container) return;

            const timeSlots = [
                { time: '00:00-06:00', action: '充电', power: '100kW', priority: '低电价充电' },
                { time: '06:00-09:00', action: '放电', power: '150kW', priority: '早高峰放电' },
                { time: '09:00-12:00', action: '待机', power: '0kW', priority: '平时段待机' },
                { time: '12:00-15:00', action: '充电', power: '120kW', priority: '光伏充电' },
                { time: '15:00-18:00', action: '待机', power: '0kW', priority: '平时段待机' },
                { time: '18:00-22:00', action: '放电', power: '180kW', priority: '晚高峰放电' },
                { time: '22:00-24:00', action: '充电', power: '80kW', priority: '低电价充电' }
            ];

            container.innerHTML = `
                <div style="max-height: 300px; overflow-y: auto;">
                    ${timeSlots.map((slot, index) => `
                        <div style="display: flex; align-items: center; padding: 12px; margin-bottom: 8px; background: #f8f9fa; border-radius: 6px; border-left: 4px solid ${getActionColor(slot.action)};">
                            <div style="flex: 1;">
                                <div style="font-weight: 600; margin-bottom: 4px;">${slot.time}</div>
                                <div style="font-size: 12px; color: #666;">${slot.priority}</div>
                            </div>
                            <div style="text-align: center; margin: 0 16px;">
                                <div style="font-size: 14px; font-weight: 600; color: ${getActionColor(slot.action)};">${slot.action}</div>
                                <div style="font-size: 12px; color: #666;">${slot.power}</div>
                            </div>
                            <button class="btn btn-sm" onclick="editTimeSlot(${index})" style="padding: 4px 8px;">
                                <i class="fas fa-edit"></i>
                            </button>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        // 获取动作颜色
        function getActionColor(action) {
            switch(action) {
                case '充电': return '#52c41a';
                case '放电': return '#f5222d';
                case '待机': return '#1890ff';
                default: return '#666';
            }
        }

        // 电价配置
        function loadElectricityPriceConfig() {
            const container = document.getElementById('electricityPriceConfig');
            if (!container) return;

            const priceConfig = [
                { period: '峰时段', time: '08:00-11:00, 18:00-23:00', price: '1.2', color: '#f5222d' },
                { period: '平时段', time: '06:00-08:00, 11:00-18:00', price: '0.8', color: '#faad14' },
                { period: '谷时段', time: '23:00-06:00', price: '0.4', color: '#52c41a' }
            ];

            container.innerHTML = `
                ${priceConfig.map(config => `
                    <div style="display: flex; align-items: center; padding: 12px; margin-bottom: 8px; background: #f8f9fa; border-radius: 6px; border-left: 4px solid ${config.color};">
                        <div style="flex: 1;">
                            <div style="font-weight: 600; color: ${config.color}; margin-bottom: 4px;">${config.period}</div>
                            <div style="font-size: 12px; color: #666;">${config.time}</div>
                        </div>
                        <div style="text-align: right;">
                            <div style="font-size: 18px; font-weight: 600; color: ${config.color};">¥${config.price}</div>
                            <div style="font-size: 12px; color: #666;">元/kWh</div>
                        </div>
                    </div>
                `).join('')}
            `;
        }

        // 初始化策略计划图表
        function initializeStrategyPlanChart() {
            const ctx = document.getElementById('strategyPlanChart');
            if (!ctx) return;

            const data = {
                labels: generateTimeLabels(24),
                datasets: [
                    {
                        label: '电价 (元/kWh)',
                        data: generateElectricityPriceData(),
                        borderColor: '#faad14',
                        backgroundColor: 'rgba(250, 173, 20, 0.1)',
                        yAxisID: 'y1',
                        type: 'line'
                    },
                    {
                        label: '充放电功率 (kW)',
                        data: generateChargeDischargePlan(),
                        backgroundColor: function(context) {
                            const value = context.parsed.y;
                            return value > 0 ? '#52c41a' : '#f5222d';
                        },
                        yAxisID: 'y'
                    }
                ]
            };

            const options = {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: '功率 (kW)'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: '电价 (元/kWh)'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            };

            new Chart(ctx, {
                type: 'bar',
                data: data,
                options: options
            });
        }

        // 生成电价数据
        function generateElectricityPriceData() {
            const prices = [];
            for (let i = 0; i < 24; i++) {
                if ((i >= 8 && i < 11) || (i >= 18 && i < 23)) {
                    prices.push(1.2); // 峰时段
                } else if ((i >= 6 && i < 8) || (i >= 11 && i < 18)) {
                    prices.push(0.8); // 平时段
                } else {
                    prices.push(0.4); // 谷时段
                }
            }
            return prices;
        }

        // 生成充放电计划数据
        function generateChargeDischargePlan() {
            const plan = [];
            for (let i = 0; i < 24; i++) {
                if ((i >= 0 && i < 6) || (i >= 12 && i < 15) || (i >= 22 && i < 24)) {
                    plan.push(Math.random() * 100 + 50); // 充电
                } else if ((i >= 6 && i < 9) || (i >= 18 && i < 22)) {
                    plan.push(-(Math.random() * 150 + 100)); // 放电
                } else {
                    plan.push(Math.random() * 20 - 10); // 待机
                }
            }
            return plan;
        }

        // 能源流向图初始化
        function initializeEnergyFlowDiagram() {
            const container = document.getElementById('energyFlowDiagram');
            if (!container) return;

            container.innerHTML = `
                <div style="position: relative; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center;">
                    <!-- 电网 -->
                    <div style="position: absolute; top: 20px; left: 50px; text-align: center;">
                        <div style="width: 80px; height: 60px; background: #1890ff; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                            <i class="fas fa-plug" style="font-size: 24px;"></i>
                        </div>
                        <div style="margin-top: 8px; font-size: 12px; font-weight: 600;">电网</div>
                        <div style="font-size: 11px; color: #666;">220V/50Hz</div>
                    </div>

                    <!-- 光伏 -->
                    <div style="position: absolute; top: 20px; right: 50px; text-align: center;">
                        <div style="width: 80px; height: 60px; background: #faad14; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                            <i class="fas fa-solar-panel" style="font-size: 24px;"></i>
                        </div>
                        <div style="margin-top: 8px; font-size: 12px; font-weight: 600;">光伏</div>
                        <div style="font-size: 11px; color: #666;">285.6kW</div>
                    </div>

                    <!-- 储能系统 -->
                    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
                        <div style="width: 120px; height: 80px; background: #52c41a; border-radius: 12px; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);">
                            <i class="fas fa-battery-three-quarters" style="font-size: 32px;"></i>
                        </div>
                        <div style="margin-top: 8px; font-size: 14px; font-weight: 600;">储能系统</div>
                        <div style="font-size: 12px; color: #666;">SOC: 85.6%</div>
                    </div>

                    <!-- 负载 -->
                    <div style="position: absolute; bottom: 20px; left: 50px; text-align: center;">
                        <div style="width: 80px; height: 60px; background: #722ed1; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                            <i class="fas fa-industry" style="font-size: 24px;"></i>
                        </div>
                        <div style="margin-top: 8px; font-size: 12px; font-weight: 600;">工厂负载</div>
                        <div style="font-size: 11px; color: #666;">342.8kW</div>
                    </div>

                    <!-- 充电桩 -->
                    <div style="position: absolute; bottom: 20px; right: 50px; text-align: center;">
                        <div style="width: 80px; height: 60px; background: #13c2c2; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                            <i class="fas fa-charging-station" style="font-size: 24px;"></i>
                        </div>
                        <div style="margin-top: 8px; font-size: 12px; font-weight: 600;">充电桩</div>
                        <div style="font-size: 11px; color: #666;">45.2kW</div>
                    </div>

                    <!-- 能源流向箭头 -->
                    <div class="energy-flow-arrow" style="position: absolute; top: 80px; left: 130px; width: 100px; height: 2px; background: #52c41a;"></div>
                    <div class="energy-flow-arrow" style="position: absolute; top: 80px; right: 130px; width: 100px; height: 2px; background: #faad14;"></div>
                    <div class="energy-flow-arrow" style="position: absolute; bottom: 80px; left: 130px; width: 100px; height: 2px; background: #722ed1;"></div>
                    <div class="energy-flow-arrow" style="position: absolute; bottom: 80px; right: 130px; width: 100px; height: 2px; background: #13c2c2;"></div>
                </div>
            `;
        }

        // 初始化输配电系统拓扑
        function initializePowerSystemTopology() {
            const container = document.getElementById('powerSystemTopology');
            if (!container) return;

            container.innerHTML = `
                <div style="position: relative; width: 100%; height: 100%; background: #f0f2f5; border-radius: 8px; padding: 20px;">
                    <div style="text-align: center; margin-bottom: 20px;">
                        <h4 style="margin: 0; color: #333;">园区输配电系统拓扑</h4>
                    </div>

                    <!-- 主变压器 -->
                    <div style="position: absolute; top: 50px; left: 50%; transform: translateX(-50%); text-align: center;">
                        <div style="width: 60px; height: 40px; background: #1890ff; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px;">
                            主变
                        </div>
                        <div style="font-size: 10px; margin-top: 4px;">10kV/380V</div>
                    </div>

                    <!-- 配电柜 -->
                    <div style="position: absolute; top: 120px; left: 30%; text-align: center;">
                        <div style="width: 40px; height: 30px; background: #52c41a; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: white; font-size: 10px;">
                            配电
                        </div>
                    </div>

                    <div style="position: absolute; top: 120px; right: 30%; text-align: center;">
                        <div style="width: 40px; height: 30px; background: #52c41a; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: white; font-size: 10px;">
                            配电
                        </div>
                    </div>

                    <!-- 储能接入点 -->
                    <div style="position: absolute; bottom: 80px; left: 50%; transform: translateX(-50%); text-align: center;">
                        <div style="width: 50px; height: 35px; background: #faad14; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: white; font-size: 10px;">
                            储能
                        </div>
                        <div style="font-size: 10px; margin-top: 4px;">PCS</div>
                    </div>

                    <!-- 连接线 -->
                    <div style="position: absolute; top: 90px; left: 50%; width: 2px; height: 30px; background: #333; transform: translateX(-50%);"></div>
                    <div style="position: absolute; top: 150px; left: 30%; width: 2px; height: 30px; background: #333;"></div>
                    <div style="position: absolute; top: 150px; right: 30%; width: 2px; height: 30px; background: #333;"></div>
                </div>
            `;
        }

        // 初始化能源流向图表
        function initializeEnergyFlowChart() {
            const ctx = document.getElementById('energyFlowChart');
            if (!ctx) return;

            const data = {
                labels: ['电网输入', '光伏输入', '储能输出', '负载消耗', '充电桩'],
                datasets: [{
                    data: [125.8, 285.6, 0, 342.8, 45.2],
                    backgroundColor: [
                        '#1890ff',
                        '#faad14',
                        '#52c41a',
                        '#722ed1',
                        '#13c2c2'
                    ]
                }]
            };

            createPieChart('energyFlowChart', data);
        }

        // 加载实时能源数据
        function loadRealtimeEnergyData() {
            const container = document.getElementById('realtimeEnergyData');
            if (!container) return;

            const energyData = [
                { source: '电网', input: '125.8', output: '0.0', status: '输入中' },
                { source: '光伏', input: '285.6', output: '159.8', status: '发电中' },
                { source: '储能', input: '125.8', output: '0.0', status: '充电中' },
                { source: '工厂负载', input: '0.0', output: '342.8', status: '运行中' },
                { source: '充电桩', input: '0.0', output: '45.2', status: '充电中' }
            ];

            container.innerHTML = `
                <div class="table-responsive">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background: #fafafa;">
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #f0f0f0;">能源节点</th>
                                <th style="padding: 12px; text-align: center; border-bottom: 1px solid #f0f0f0;">输入功率 (kW)</th>
                                <th style="padding: 12px; text-align: center; border-bottom: 1px solid #f0f0f0;">输出功率 (kW)</th>
                                <th style="padding: 12px; text-align: center; border-bottom: 1px solid #f0f0f0;">运行状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${energyData.map(item => `
                                <tr>
                                    <td style="padding: 12px; border-bottom: 1px solid #f0f0f0; font-weight: 500;">${item.source}</td>
                                    <td style="padding: 12px; text-align: center; border-bottom: 1px solid #f0f0f0; color: #52c41a; font-weight: 600;">${item.input}</td>
                                    <td style="padding: 12px; text-align: center; border-bottom: 1px solid #f0f0f0; color: #f5222d; font-weight: 600;">${item.output}</td>
                                    <td style="padding: 12px; text-align: center; border-bottom: 1px solid #f0f0f0;">
                                        <span style="padding: 4px 8px; background: #f6ffed; color: #52c41a; border-radius: 12px; font-size: 12px;">${item.status}</span>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
        }

        // 加载AI节能建议
        function loadEnergySuggestions() {
            const container = document.getElementById('energySuggestions');
            if (!container) return;

            const suggestions = [
                {
                    title: '水泵变频改造建议',
                    description: '建议对3号车间循环水泵进行变频改造，预计节能15%',
                    priority: 'high',
                    savings: '2,400元/月',
                    status: '待实施'
                },
                {
                    title: '照明系统优化',
                    description: '建议将传统照明更换为LED智能照明系统',
                    priority: 'medium',
                    savings: '1,200元/月',
                    status: '评估中'
                },
                {
                    title: '空调系统调优',
                    description: '优化空调运行时间和温度设定，避免能源浪费',
                    priority: 'medium',
                    savings: '800元/月',
                    status: '已实施'
                },
                {
                    title: '储能充电策略优化',
                    description: '调整储能充电时间，更多利用谷时电价和光伏发电',
                    priority: 'high',
                    savings: '3,200元/月',
                    status: '执行中'
                }
            ];

            container.innerHTML = `
                <div style="max-height: 350px; overflow-y: auto;">
                    ${suggestions.map(suggestion => `
                        <div style="padding: 16px; margin-bottom: 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid ${getPriorityColor(suggestion.priority)};">
                            <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;">
                                <h4 style="margin: 0; font-size: 16px; color: #333;">${suggestion.title}</h4>
                                <span style="padding: 4px 8px; background: ${getPriorityBg(suggestion.priority)}; color: ${getPriorityColor(suggestion.priority)}; border-radius: 12px; font-size: 12px; font-weight: 500;">
                                    ${suggestion.priority === 'high' ? '高优先级' : suggestion.priority === 'medium' ? '中优先级' : '低优先级'}
                                </span>
                            </div>
                            <p style="margin: 8px 0; color: #666; font-size: 14px;">${suggestion.description}</p>
                            <div style="display: flex; align-items: center; justify-content: space-between;">
                                <div style="display: flex; align-items: center; gap: 16px;">
                                    <span style="font-size: 14px; color: #52c41a; font-weight: 600;">节省: ${suggestion.savings}</span>
                                    <span style="font-size: 12px; color: #666;">状态: ${suggestion.status}</span>
                                </div>
                                <button class="btn btn-sm btn-primary" onclick="implementSuggestion('${suggestion.title}')">
                                    <i class="fas fa-check"></i>
                                    采纳建议
                                </button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        // 获取优先级颜色
        function getPriorityColor(priority) {
            switch(priority) {
                case 'high': return '#f5222d';
                case 'medium': return '#faad14';
                case 'low': return '#52c41a';
                default: return '#666';
            }
        }

        // 获取优先级背景色
        function getPriorityBg(priority) {
            switch(priority) {
                case 'high': return '#fff2f0';
                case 'medium': return '#fffbe6';
                case 'low': return '#f6ffed';
                default: return '#f5f5f5';
            }
        }

        // 初始化峰谷电价优化图表
        function initializePeakValleyOptimizationChart() {
            const ctx = document.getElementById('peakValleyOptimizationChart');
            if (!ctx) return;

            const data = {
                labels: generateTimeLabels(24),
                datasets: [
                    {
                        label: '优化前成本',
                        data: generateCostData('before'),
                        borderColor: '#f5222d',
                        backgroundColor: 'rgba(245, 34, 45, 0.1)',
                        fill: true
                    },
                    {
                        label: '优化后成本',
                        data: generateCostData('after'),
                        borderColor: '#52c41a',
                        backgroundColor: 'rgba(82, 196, 26, 0.1)',
                        fill: true
                    }
                ]
            };

            createLineChart('peakValleyOptimizationChart', data);
        }

        // 生成成本数据
        function generateCostData(type) {
            const data = [];
            for (let i = 0; i < 24; i++) {
                const basePrice = generateElectricityPriceData()[i];
                const consumption = Math.random() * 100 + 50;
                const baseCost = basePrice * consumption;

                if (type === 'before') {
                    data.push(baseCost);
                } else {
                    // 优化后成本降低20-40%
                    data.push(baseCost * (0.6 + Math.random() * 0.2));
                }
            }
            return data;
        }

        // 初始化能效趋势图表
        function initializeEfficiencyTrendChart() {
            const ctx = document.getElementById('efficiencyTrendChart');
            if (!ctx) return;

            const data = {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                datasets: [{
                    label: '系统效率 (%)',
                    data: [88.5, 89.2, 91.1, 92.3, 93.8, 94.2],
                    borderColor: '#1890ff',
                    backgroundColor: 'rgba(24, 144, 255, 0.1)',
                    fill: true,
                    tension: 0.4
                }]
            };

            createLineChart('efficiencyTrendChart', data);
        }

        // 初始化成本优化图表
        function initializeCostOptimizationChart() {
            const ctx = document.getElementById('costOptimizationChart');
            if (!ctx) return;

            const data = {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                datasets: [
                    {
                        label: '优化前成本',
                        data: [45000, 42000, 48000, 46000, 44000, 43000],
                        backgroundColor: '#f5222d'
                    },
                    {
                        label: '优化后成本',
                        data: [38000, 35000, 39000, 37000, 35500, 34000],
                        backgroundColor: '#52c41a'
                    }
                ]
            };

            createBarChart('costOptimizationChart', data);
        }

        // 加载智能调度策略
        function loadIntelligentSchedulingStrategy() {
            const container = document.getElementById('intelligentSchedulingStrategy');
            if (!container) return;

            container.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #1890ff;">
                        <h4 style="margin: 0 0 12px 0; color: #1890ff;">
                            <i class="fas fa-clock" style="margin-right: 8px;"></i>
                            时间优化策略
                        </h4>
                        <p style="margin: 0 0 12px 0; color: #666; font-size: 14px;">
                            基于历史用电数据和电价波动，智能调整充放电时间
                        </p>
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <span style="font-size: 12px; color: #52c41a;">节能效果: +12.5%</span>
                            <button class="btn btn-sm btn-primary" onclick="enableTimeOptimization()">启用</button>
                        </div>
                    </div>

                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #52c41a;">
                        <h4 style="margin: 0 0 12px 0; color: #52c41a;">
                            <i class="fas fa-leaf" style="margin-right: 8px;"></i>
                            绿色能源优先
                        </h4>
                        <p style="margin: 0 0 12px 0; color: #666; font-size: 14px;">
                            优先使用光伏等可再生能源进行储能充电
                        </p>
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <span style="font-size: 12px; color: #52c41a;">绿电占比: +28.3%</span>
                            <button class="btn btn-sm btn-success" onclick="enableGreenEnergyPriority()">启用</button>
                        </div>
                    </div>

                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #faad14;">
                        <h4 style="margin: 0 0 12px 0; color: #faad14;">
                            <i class="fas fa-chart-line" style="margin-right: 8px;"></i>
                            负载预测调度
                        </h4>
                        <p style="margin: 0 0 12px 0; color: #666; font-size: 14px;">
                            基于AI预测未来负载需求，提前调整储能策略
                        </p>
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <span style="font-size: 12px; color: #52c41a;">预测准确率: 94.2%</span>
                            <button class="btn btn-sm btn-warning" onclick="enableLoadPrediction()">启用</button>
                        </div>
                    </div>
                </div>
            `;
        }

        // 功能函数
        function refreshRealtimeData() {
            loadStorageParameters();
            loadGridInteractionData();
            loadSolarIntegrationData();
            showNotification('实时数据已刷新', 'success');
        }

        function refreshSolarData() {
            loadSolarIntegrationData();
            showNotification('光伏数据已刷新', 'success');
        }

        function exportRealtimeData() {
            showNotification('实时数据导出功能开发中...', 'info');
        }

        function updateOperationMode() {
            const mode = document.getElementById('operationMode').value;
            showNotification(`运行模式已切换为: ${mode}`, 'success');
        }

        function updateChargePowerLimit(value) {
            document.getElementById('chargePowerValue').textContent = value + ' kW';
        }

        function updateDischargePowerLimit(value) {
            document.getElementById('dischargePowerValue').textContent = value + ' kW';
        }

        function applyStrategy() {
            showNotification('充放电策略已应用', 'success');
        }

        function resetStrategy() {
            showNotification('策略已重置为默认设置', 'info');
        }

        function editTimeSlot(index) {
            showNotification(`编辑时段 ${index + 1} 配置`, 'info');
        }

        function refreshPeakValleyStrategy() {
            showNotification('峰谷电价策略已刷新', 'success');
        }

        function optimizePeakValleyStrategy() {
            showNotification('正在进行智能优化...', 'info');
            setTimeout(() => {
                showNotification('峰谷电价策略优化完成，预计节省15.8%电费', 'success');
            }, 2000);
        }

        function startCharging() {
            showNotification('储能系统开始充电', 'success');
        }

        function stopCharging() {
            showNotification('储能系统停止充电', 'warning');
        }

        function startDischarging() {
            showNotification('储能系统开始放电', 'warning');
        }

        function setStandbyMode() {
            showNotification('储能系统切换为待机模式', 'info');
        }

        function refreshEnergyFlow() {
            initializeEnergyFlowDiagram();
            loadRealtimeEnergyData();
            showNotification('能源流向数据已刷新', 'success');
        }

        function toggleFlowAnimation() {
            showNotification('能源流向动画功能开发中...', 'info');
        }

        function generateNewSuggestions() {
            loadEnergySuggestions();
            showNotification('AI已生成新的节能建议', 'success');
        }

        function runAIAnalysis() {
            showNotification('正在运行AI分析...', 'info');
            setTimeout(() => {
                showNotification('AI分析完成，发现3个优化机会', 'success');
            }, 3000);
        }

        function exportAnalysisReport() {
            showNotification('分析报告导出功能开发中...', 'info');
        }

        function implementSuggestion(title) {
            showNotification(`正在实施建议: ${title}`, 'success');
        }

        function enableTimeOptimization() {
            showNotification('时间优化策略已启用', 'success');
        }

        function enableGreenEnergyPriority() {
            showNotification('绿色能源优先策略已启用', 'success');
        }

        function enableLoadPrediction() {
            showNotification('负载预测调度已启用', 'success');
        }

        // 通知函数
        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 6px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                transition: all 0.3s ease;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            `;

            // 设置颜色
            switch(type) {
                case 'success':
                    notification.style.background = '#52c41a';
                    break;
                case 'warning':
                    notification.style.background = '#faad14';
                    break;
                case 'error':
                    notification.style.background = '#f5222d';
                    break;
                default:
                    notification.style.background = '#1890ff';
            }

            notification.textContent = message;
            document.body.appendChild(notification);

            // 3秒后自动移除
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // 添加CSS样式
        const style = document.createElement('style');
        style.textContent = `
            .form-control {
                width: 100%;
                padding: 8px 12px;
                border: 1px solid #d9d9d9;
                border-radius: 4px;
                font-size: 14px;
                transition: border-color 0.3s;
            }

            .form-control:focus {
                outline: none;
                border-color: #1890ff;
                box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            }

            .btn-sm {
                padding: 4px 8px;
                font-size: 12px;
            }

            .table-responsive {
                overflow-x: auto;
            }

            @keyframes energyFlow {
                0% { opacity: 0.3; }
                50% { opacity: 1; }
                100% { opacity: 0.3; }
            }

            .energy-flow-arrow {
                animation: energyFlow 2s infinite;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
