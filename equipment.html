<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备管理 - 智慧能源管理平台</title>
    
    <!-- 外部CSS库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    
    <style>
        .equipment-tabs {
            display: flex;
            background: white;
            border-radius: 8px 8px 0 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 0;
        }
        
        .tab-button {
            flex: 1;
            padding: 16px 24px;
            border: none;
            background: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #8c8c8c;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }
        
        .tab-button.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
            background: #f8f9fa;
        }
        
        .tab-content {
            background: white;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 24px;
        }
        
        .equipment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .equipment-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #1890ff;
        }
        
        .equipment-card.warning {
            border-left-color: #faad14;
        }
        
        .equipment-card.error {
            border-left-color: #f5222d;
        }
        
        .equipment-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }
        
        .equipment-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: #1890ff;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        
        .equipment-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .equipment-info {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 16px;
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .info-label {
            font-size: 12px;
            color: #8c8c8c;
        }
        
        .info-value {
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }
        
        .work-order-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        .work-order-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #1890ff;
        }
        
        .work-order-item.urgent {
            border-left-color: #f5222d;
            background: #fff2f0;
        }
        
        .work-order-item.normal {
            border-left-color: #faad14;
            background: #fffbe6;
        }
        
        .order-info {
            flex: 1;
        }
        
        .order-title {
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }
        
        .order-meta {
            font-size: 12px;
            color: #8c8c8c;
        }
        
        .order-actions {
            display: flex;
            gap: 8px;
        }
        
        .maintenance-schedule {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .schedule-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .schedule-date {
            width: 80px;
            text-align: center;
            font-size: 12px;
            color: #8c8c8c;
        }
        
        .schedule-content {
            flex: 1;
            margin-left: 16px;
        }
        
        .schedule-title {
            font-weight: 500;
            color: #333;
        }
        
        .schedule-device {
            font-size: 12px;
            color: #8c8c8c;
            margin-top: 2px;
        }
        
        .add-equipment-btn {
            position: fixed;
            bottom: 24px;
            right: 24px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: #1890ff;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 16px rgba(24,144,255,0.3);
            transition: all 0.3s;
        }
        
        .add-equipment-btn:hover {
            background: #40a9ff;
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化页面布局
            initializeLayout('设备管理', ['运营中心', '设备管理']);
            
            // 获取页面内容容器
            const pageContent = document.getElementById('page-content');
            
            // 插入页面内容
            pageContent.innerHTML = `
                <!-- 标签页导航 -->
                <div class="equipment-tabs">
                    <button class="tab-button active" onclick="switchTab('devices')">
                        <i class="fas fa-cogs" style="margin-right: 8px;"></i>
                        设备台账
                    </button>
                    <button class="tab-button" onclick="switchTab('workorders')">
                        <i class="fas fa-clipboard-list" style="margin-right: 8px;"></i>
                        工单管理
                    </button>
                    <button class="tab-button" onclick="switchTab('maintenance')">
                        <i class="fas fa-calendar-check" style="margin-right: 8px;"></i>
                        维护计划
                    </button>
                </div>

                <!-- 设备台账 -->
                <div class="tab-content" id="devicesTab">
                    <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 20px;">
                        <div style="display: flex; gap: 16px; align-items: center;">
                            <select class="control-input" onchange="filterDevices()">
                                <option value="all">全部设备</option>
                                <option value="hvac">空调系统</option>
                                <option value="lighting">照明系统</option>
                                <option value="elevator">电梯系统</option>
                                <option value="water">供水系统</option>
                            </select>
                            <select class="control-input" onchange="filterDevices()">
                                <option value="all">全部状态</option>
                                <option value="normal">正常</option>
                                <option value="warning">告警</option>
                                <option value="error">故障</option>
                            </select>
                        </div>
                        <button class="btn btn-primary" onclick="addEquipment()">
                            <i class="fas fa-plus"></i>
                            添加设备
                        </button>
                    </div>
                    
                    <div class="equipment-grid">
                        <div class="equipment-card">
                            <div class="equipment-header">
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <div class="equipment-icon">
                                        <i class="fas fa-snowflake"></i>
                                    </div>
                                    <div>
                                        <h4 style="margin: 0; font-size: 16px;">中央空调-01</h4>
                                        <p style="margin: 2px 0 0 0; font-size: 12px; color: #8c8c8c;">3F-AC-01</p>
                                    </div>
                                </div>
                                <div class="equipment-status status-success">正常</div>
                            </div>
                            <div class="equipment-info">
                                <div class="info-item">
                                    <div class="info-label">功率</div>
                                    <div class="info-value">120.5 kW</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">运行时间</div>
                                    <div class="info-value">2,340 h</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">最后维护</div>
                                    <div class="info-value">2024-01-10</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">下次维护</div>
                                    <div class="info-value">2024-02-10</div>
                                </div>
                            </div>
                            <div style="display: flex; gap: 8px;">
                                <button class="btn btn-primary" onclick="viewDevice(1)">详情</button>
                                <button class="btn" onclick="editDevice(1)">编辑</button>
                            </div>
                        </div>

                        <div class="equipment-card warning">
                            <div class="equipment-header">
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <div class="equipment-icon" style="background: #faad14;">
                                        <i class="fas fa-elevator"></i>
                                    </div>
                                    <div>
                                        <h4 style="margin: 0; font-size: 16px;">电梯系统-01</h4>
                                        <p style="margin: 2px 0 0 0; font-size: 12px; color: #8c8c8c;">EL-01</p>
                                    </div>
                                </div>
                                <div class="equipment-status status-warning">告警</div>
                            </div>
                            <div class="equipment-info">
                                <div class="info-item">
                                    <div class="info-label">功率</div>
                                    <div class="info-value">89.7 kW</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">运行时间</div>
                                    <div class="info-value">5,680 h</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">最后维护</div>
                                    <div class="info-value">2023-12-15</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">下次维护</div>
                                    <div class="info-value">逾期</div>
                                </div>
                            </div>
                            <div style="display: flex; gap: 8px;">
                                <button class="btn btn-primary" onclick="viewDevice(2)">详情</button>
                                <button class="btn btn-warning" onclick="createWorkOrder(2)">报修</button>
                            </div>
                        </div>

                        <div class="equipment-card">
                            <div class="equipment-header">
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <div class="equipment-icon" style="background: #52c41a;">
                                        <i class="fas fa-lightbulb"></i>
                                    </div>
                                    <div>
                                        <h4 style="margin: 0; font-size: 16px;">照明系统-A区</h4>
                                        <p style="margin: 2px 0 0 0; font-size: 12px; color: #8c8c8c;">LT-A01</p>
                                    </div>
                                </div>
                                <div class="equipment-status status-success">正常</div>
                            </div>
                            <div class="equipment-info">
                                <div class="info-item">
                                    <div class="info-label">功率</div>
                                    <div class="info-value">45.2 kW</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">运行时间</div>
                                    <div class="info-value">1,890 h</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">最后维护</div>
                                    <div class="info-value">2024-01-05</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">下次维护</div>
                                    <div class="info-value">2024-04-05</div>
                                </div>
                            </div>
                            <div style="display: flex; gap: 8px;">
                                <button class="btn btn-primary" onclick="viewDevice(3)">详情</button>
                                <button class="btn" onclick="editDevice(3)">编辑</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 工单管理 -->
                <div class="tab-content" id="workordersTab" style="display: none;">
                    <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 20px;">
                        <div style="display: flex; gap: 16px; align-items: center;">
                            <select class="control-input">
                                <option value="all">全部工单</option>
                                <option value="pending">待处理</option>
                                <option value="processing">处理中</option>
                                <option value="completed">已完成</option>
                            </select>
                            <select class="control-input">
                                <option value="all">全部优先级</option>
                                <option value="urgent">紧急</option>
                                <option value="normal">普通</option>
                                <option value="low">低</option>
                            </select>
                        </div>
                        <button class="btn btn-primary" onclick="createWorkOrder()">
                            <i class="fas fa-plus"></i>
                            创建工单
                        </button>
                    </div>
                    
                    <div class="work-order-list">
                        <div class="work-order-item urgent">
                            <div class="order-info">
                                <div class="order-title">电梯系统功率异常维修</div>
                                <div class="order-meta">
                                    设备: EL-01 | 创建时间: 2024-01-15 14:30 | 优先级: 紧急
                                </div>
                            </div>
                            <div class="order-actions">
                                <button class="btn btn-primary">处理</button>
                                <button class="btn">详情</button>
                            </div>
                        </div>
                        
                        <div class="work-order-item normal">
                            <div class="order-info">
                                <div class="order-title">空调系统定期保养</div>
                                <div class="order-meta">
                                    设备: 3F-AC-01 | 创建时间: 2024-01-14 09:15 | 优先级: 普通
                                </div>
                            </div>
                            <div class="order-actions">
                                <button class="btn btn-primary">处理</button>
                                <button class="btn">详情</button>
                            </div>
                        </div>
                        
                        <div class="work-order-item">
                            <div class="order-info">
                                <div class="order-title">照明系统巡检</div>
                                <div class="order-meta">
                                    设备: LT-A01 | 创建时间: 2024-01-13 16:20 | 优先级: 低
                                </div>
                            </div>
                            <div class="order-actions">
                                <button class="btn btn-success">已完成</button>
                                <button class="btn">详情</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 维护计划 -->
                <div class="tab-content" id="maintenanceTab" style="display: none;">
                    <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 20px;">
                        <h3 style="margin: 0; font-size: 18px; font-weight: 600;">维护计划安排</h3>
                        <button class="btn btn-primary" onclick="addMaintenancePlan()">
                            <i class="fas fa-plus"></i>
                            添加计划
                        </button>
                    </div>
                    
                    <div class="maintenance-schedule">
                        <div class="schedule-item">
                            <div class="schedule-date">
                                <div style="font-weight: bold; color: #1890ff;">15</div>
                                <div>今天</div>
                            </div>
                            <div class="schedule-content">
                                <div class="schedule-title">空调系统清洁保养</div>
                                <div class="schedule-device">设备: 3F-AC-01, 4F-AC-02</div>
                            </div>
                            <button class="btn btn-primary">开始</button>
                        </div>
                        
                        <div class="schedule-item">
                            <div class="schedule-date">
                                <div style="font-weight: bold; color: #8c8c8c;">18</div>
                                <div>周四</div>
                            </div>
                            <div class="schedule-content">
                                <div class="schedule-title">电梯系统安全检查</div>
                                <div class="schedule-device">设备: EL-01, EL-02</div>
                            </div>
                            <button class="btn">计划中</button>
                        </div>
                        
                        <div class="schedule-item">
                            <div class="schedule-date">
                                <div style="font-weight: bold; color: #8c8c8c;">22</div>
                                <div>下周一</div>
                            </div>
                            <div class="schedule-content">
                                <div class="schedule-title">供水系统管道检修</div>
                                <div class="schedule-device">设备: WS-01</div>
                            </div>
                            <button class="btn">计划中</button>
                        </div>
                    </div>
                </div>

                <!-- 添加设备按钮 -->
                <button class="add-equipment-btn" onclick="addEquipment()" title="添加设备">
                    <i class="fas fa-plus"></i>
                </button>
            `;
        });
        
        // 切换标签页
        function switchTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.style.display = 'none';
            });
            
            // 移除所有按钮的活跃状态
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 显示选中的标签页
            document.getElementById(tabName + 'Tab').style.display = 'block';
            
            // 激活对应按钮
            event.target.classList.add('active');
        }
        
        // 查看设备详情
        function viewDevice(deviceId) {
            showNotification(`正在查看设备 #${deviceId} 详情`, 'info');
        }
        
        // 编辑设备
        function editDevice(deviceId) {
            showNotification(`正在编辑设备 #${deviceId}`, 'info');
        }
        
        // 创建工单
        function createWorkOrder(deviceId) {
            if (deviceId) {
                showNotification(`正在为设备 #${deviceId} 创建工单`, 'info');
            } else {
                showNotification('正在创建新工单', 'info');
            }
        }
        
        // 添加设备
        function addEquipment() {
            showNotification('正在添加新设备', 'info');
        }
        
        // 添加维护计划
        function addMaintenancePlan() {
            showNotification('正在添加维护计划', 'info');
        }
        
        // 过滤设备
        function filterDevices() {
            showNotification('设备列表已更新', 'info');
        }
    </script>
    
    <!-- 引入公共脚本 -->
    <script src="assets/js/common.js"></script>
</body>
</html>
