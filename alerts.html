<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>告警管理 - 智慧能源管理平台</title>
    
    <!-- 外部CSS库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    
    <!-- 外部JS库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        .alert-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .alert-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .alert-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
        }
        
        .alert-card.critical::before { background: #f5222d; }
        .alert-card.warning::before { background: #faad14; }
        .alert-card.info::before { background: #1890ff; }
        .alert-card.resolved::before { background: #52c41a; }
        
        .alert-count {
            font-size: 32px;
            font-weight: bold;
            margin: 8px 0;
        }
        
        .alert-count.critical { color: #f5222d; }
        .alert-count.warning { color: #faad14; }
        .alert-count.info { color: #1890ff; }
        .alert-count.resolved { color: #52c41a; }
        
        .alert-filters {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .filter-row {
            display: flex;
            align-items: center;
            gap: 16px;
            flex-wrap: wrap;
        }
        
        .alert-table {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .alert-row {
            display: grid;
            grid-template-columns: auto 2fr 1fr 1fr 1fr auto;
            align-items: center;
            padding: 16px 20px;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.3s;
        }
        
        .alert-row:hover {
            background: #f8f9fa;
        }
        
        .alert-row.header {
            background: #fafafa;
            font-weight: 600;
            border-bottom: 2px solid #d9d9d9;
        }
        
        .alert-level {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .level-critical { background: #f5222d; }
        .level-warning { background: #faad14; }
        .level-info { background: #1890ff; }
        
        .alert-actions {
            display: flex;
            gap: 8px;
        }
        
        .action-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .action-btn.resolve {
            background: #52c41a;
            color: white;
        }
        
        .action-btn.ignore {
            background: #8c8c8c;
            color: white;
        }
        
        .action-btn.detail {
            background: #1890ff;
            color: white;
        }
        
        .alert-detail-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .modal-content {
            background: white;
            border-radius: 8px;
            padding: 24px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 1px solid #d9d9d9;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #8c8c8c;
        }
        
        .alert-timeline {
            margin-top: 20px;
        }
        
        .timeline-item {
            display: flex;
            gap: 12px;
            margin-bottom: 16px;
        }
        
        .timeline-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #1890ff;
            margin-top: 4px;
            flex-shrink: 0;
        }
        
        .timeline-content {
            flex: 1;
        }
        
        .timeline-time {
            font-size: 12px;
            color: #8c8c8c;
        }
        
        @media (max-width: 768px) {
            .alert-row {
                grid-template-columns: 1fr;
                gap: 8px;
                text-align: left;
            }
            
            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化页面布局
            initializeLayout('告警管理', ['运营中心', '告警管理']);
            
            // 获取页面内容容器
            const pageContent = document.getElementById('page-content');
            
            // 插入页面内容
            pageContent.innerHTML = `
                <!-- 告警统计概览 -->
                <div class="alert-summary">
                    <div class="alert-card critical">
                        <div style="font-size: 14px; color: #8c8c8c;">严重告警</div>
                        <div class="alert-count critical">3</div>
                        <div style="font-size: 12px; color: #8c8c8c;">需要立即处理</div>
                    </div>
                    <div class="alert-card warning">
                        <div style="font-size: 14px; color: #8c8c8c;">警告告警</div>
                        <div class="alert-count warning">12</div>
                        <div style="font-size: 12px; color: #8c8c8c;">需要关注</div>
                    </div>
                    <div class="alert-card info">
                        <div style="font-size: 14px; color: #8c8c8c;">信息告警</div>
                        <div class="alert-count info">8</div>
                        <div style="font-size: 12px; color: #8c8c8c;">一般提醒</div>
                    </div>
                    <div class="alert-card resolved">
                        <div style="font-size: 14px; color: #8c8c8c;">已处理</div>
                        <div class="alert-count resolved">45</div>
                        <div style="font-size: 12px; color: #8c8c8c;">今日已解决</div>
                    </div>
                </div>

                <!-- 告警过滤器 -->
                <div class="alert-filters">
                    <div class="filter-row">
                        <div class="control-group">
                            <label class="control-label">告警级别</label>
                            <select class="control-input" id="alertLevel" onchange="filterAlerts()">
                                <option value="all">全部</option>
                                <option value="critical">严重</option>
                                <option value="warning">警告</option>
                                <option value="info">信息</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <label class="control-label">设备类型</label>
                            <select class="control-input" id="deviceType" onchange="filterAlerts()">
                                <option value="all">全部设备</option>
                                <option value="hvac">空调系统</option>
                                <option value="lighting">照明系统</option>
                                <option value="elevator">电梯系统</option>
                                <option value="water">供水系统</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <label class="control-label">状态</label>
                            <select class="control-input" id="alertStatus" onchange="filterAlerts()">
                                <option value="active">活跃</option>
                                <option value="resolved">已解决</option>
                                <option value="ignored">已忽略</option>
                            </select>
                        </div>
                        <button class="btn btn-primary" onclick="refreshAlerts()">
                            <i class="fas fa-sync-alt"></i>
                            刷新
                        </button>
                        <button class="btn btn-success" onclick="batchResolve()">
                            <i class="fas fa-check-double"></i>
                            批量处理
                        </button>
                    </div>
                </div>

                <!-- 告警列表 -->
                <div class="alert-table">
                    <div class="alert-row header">
                        <div><input type="checkbox" id="selectAll" onchange="toggleSelectAll()"></div>
                        <div>告警信息</div>
                        <div>设备/位置</div>
                        <div>级别</div>
                        <div>发生时间</div>
                        <div>操作</div>
                    </div>
                    <div id="alertList">
                        <!-- 告警数据将通过JavaScript动态插入 -->
                    </div>
                </div>

                <!-- 告警详情模态框 -->
                <div class="alert-detail-modal" id="alertModal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3 id="modalTitle">告警详情</h3>
                            <button class="close-btn" onclick="closeModal()">&times;</button>
                        </div>
                        <div id="modalBody">
                            <!-- 详情内容将动态插入 -->
                        </div>
                    </div>
                </div>
            `;
            
            // 初始化告警页面
            initializeAlerts();
        });
        
        // 模拟告警数据
        const alertsData = [
            {
                id: 1,
                level: 'critical',
                message: '3楼空调温度传感器故障',
                device: '空调系统 - 3F-AC-01',
                location: 'A栋3楼',
                time: '2024-01-15 14:30:25',
                description: '温度传感器读数异常，可能影响空调自动调节功能',
                status: 'active'
            },
            {
                id: 2,
                level: 'warning',
                message: '电梯系统功率异常',
                device: '电梯系统 - EL-01',
                location: 'A栋电梯',
                time: '2024-01-15 14:28:12',
                description: '电梯运行功率超出正常范围15%',
                status: 'active'
            },
            {
                id: 3,
                level: 'info',
                message: '用电量接近峰值',
                device: '配电系统',
                location: '主配电室',
                time: '2024-01-15 14:25:08',
                description: '当前用电量已达到设定峰值的90%',
                status: 'active'
            }
        ];
        
        // 初始化告警页面
        function initializeAlerts() {
            renderAlertList();
        }
        
        // 渲染告警列表
        function renderAlertList() {
            const alertList = document.getElementById('alertList');
            let html = '';
            
            alertsData.forEach(alert => {
                const levelClass = `level-${alert.level}`;
                const levelText = alert.level === 'critical' ? '严重' : 
                                 alert.level === 'warning' ? '警告' : '信息';
                
                html += `
                    <div class="alert-row">
                        <div><input type="checkbox" value="${alert.id}"></div>
                        <div>
                            <div style="display: flex; align-items: center;">
                                <div class="alert-level ${levelClass}"></div>
                                <div>
                                    <div style="font-weight: 500;">${alert.message}</div>
                                    <div style="font-size: 12px; color: #8c8c8c; margin-top: 2px;">
                                        ${alert.description}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div style="font-weight: 500;">${alert.device}</div>
                            <div style="font-size: 12px; color: #8c8c8c;">${alert.location}</div>
                        </div>
                        <div>
                            <span class="status-tag status-${alert.level === 'critical' ? 'danger' : alert.level === 'warning' ? 'warning' : 'success'}">
                                ${levelText}
                            </span>
                        </div>
                        <div style="font-size: 12px;">${alert.time}</div>
                        <div class="alert-actions">
                            <button class="action-btn detail" onclick="showAlertDetail(${alert.id})">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="action-btn resolve" onclick="resolveAlert(${alert.id})">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="action-btn ignore" onclick="ignoreAlert(${alert.id})">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                `;
            });
            
            alertList.innerHTML = html;
        }
        
        // 显示告警详情
        function showAlertDetail(alertId) {
            const alert = alertsData.find(a => a.id === alertId);
            if (!alert) return;
            
            const modal = document.getElementById('alertModal');
            const modalTitle = document.getElementById('modalTitle');
            const modalBody = document.getElementById('modalBody');
            
            modalTitle.textContent = alert.message;
            modalBody.innerHTML = `
                <div style="margin-bottom: 20px;">
                    <h4>基本信息</h4>
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px; margin-top: 12px;">
                        <div>
                            <strong>设备:</strong> ${alert.device}
                        </div>
                        <div>
                            <strong>位置:</strong> ${alert.location}
                        </div>
                        <div>
                            <strong>级别:</strong> 
                            <span class="status-tag status-${alert.level === 'critical' ? 'danger' : alert.level === 'warning' ? 'warning' : 'success'}">
                                ${alert.level === 'critical' ? '严重' : alert.level === 'warning' ? '警告' : '信息'}
                            </span>
                        </div>
                        <div>
                            <strong>发生时间:</strong> ${alert.time}
                        </div>
                    </div>
                </div>
                
                <div style="margin-bottom: 20px;">
                    <h4>详细描述</h4>
                    <p style="margin-top: 8px; padding: 12px; background: #f8f9fa; border-radius: 4px;">
                        ${alert.description}
                    </p>
                </div>
                
                <div class="alert-timeline">
                    <h4>处理时间线</h4>
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <div>告警触发</div>
                            <div class="timeline-time">${alert.time}</div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-dot" style="background: #faad14;"></div>
                        <div class="timeline-content">
                            <div>系统自动通知相关人员</div>
                            <div class="timeline-time">2024-01-15 14:31:00</div>
                        </div>
                    </div>
                </div>
                
                <div style="margin-top: 24px; text-align: right;">
                    <button class="btn btn-success" onclick="resolveAlert(${alert.id}); closeModal();">
                        <i class="fas fa-check"></i>
                        标记为已解决
                    </button>
                    <button class="btn btn-primary" onclick="closeModal();" style="margin-left: 8px;">
                        关闭
                    </button>
                </div>
            `;
            
            modal.style.display = 'flex';
        }
        
        // 关闭模态框
        function closeModal() {
            document.getElementById('alertModal').style.display = 'none';
        }
        
        // 解决告警
        function resolveAlert(alertId) {
            showNotification(`告警 #${alertId} 已标记为已解决`, 'success');
            // 这里可以添加实际的解决逻辑
        }
        
        // 忽略告警
        function ignoreAlert(alertId) {
            showNotification(`告警 #${alertId} 已忽略`, 'info');
        }
        
        // 过滤告警
        function filterAlerts() {
            showNotification('告警列表已更新', 'info');
        }
        
        // 刷新告警
        function refreshAlerts() {
            renderAlertList();
            showNotification('告警列表已刷新', 'success');
        }
        
        // 批量处理
        function batchResolve() {
            const checkboxes = document.querySelectorAll('#alertList input[type="checkbox"]:checked');
            if (checkboxes.length === 0) {
                showNotification('请选择要处理的告警', 'warning');
                return;
            }
            
            showNotification(`已批量处理 ${checkboxes.length} 个告警`, 'success');
        }
        
        // 全选/取消全选
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('#alertList input[type="checkbox"]');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
        }
    </script>
    
    <!-- 引入公共脚本 -->
    <script src="assets/js/common.js"></script>
    <script src="assets/js/charts.js"></script>
</body>
</html>
