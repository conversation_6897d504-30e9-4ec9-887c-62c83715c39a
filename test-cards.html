<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据卡片测试页面</title>
    
    <!-- 外部CSS库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f0f2f5;
            padding: 20px;
            margin: 0;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }
        
        .card-container {
            min-height: 140px;
            display: flex;
            align-items: stretch;
        }
        
        /* 移动端测试 */
        @media (max-width: 768px) {
            .test-grid {
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 12px;
            }
        }
        
        @media (max-width: 480px) {
            .test-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>数据卡片显示测试</h1>
        
        <div class="test-section">
            <h2>桌面端显示测试 (4列布局)</h2>
            <div class="test-grid">
                <div id="test-card-1" class="card-container"></div>
                <div id="test-card-2" class="card-container"></div>
                <div id="test-card-3" class="card-container"></div>
                <div id="test-card-4" class="card-container"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>长文本测试</h2>
            <div class="test-grid">
                <div id="test-card-5" class="card-container"></div>
                <div id="test-card-6" class="card-container"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>不同数值大小测试</h2>
            <div class="test-grid">
                <div id="test-card-7" class="card-container"></div>
                <div id="test-card-8" class="card-container"></div>
                <div id="test-card-9" class="card-container"></div>
            </div>
        </div>
    </div>

    <script src="assets/js/charts.js"></script>
    <script>
        // 测试不同的数据卡片
        document.addEventListener('DOMContentLoaded', function() {
            // 基础测试
            createDataCard('test-card-1', '总用电量', 1247.8, 'kWh', 2.3, 'fas fa-bolt');
            createDataCard('test-card-2', '总用水量', 89.5, 'm³', -1.2, 'fas fa-tint');
            createDataCard('test-card-3', '总用气量', 34.7, 'm³', 0.8, 'fas fa-fire');
            createDataCard('test-card-4', '能效比', 85.6, '%', 1.5, 'fas fa-leaf');
            
            // 长文本测试
            createDataCard('test-card-5', '建筑物总能耗消费量', 12345.67, 'kWh/月', 15.8, 'fas fa-building');
            createDataCard('test-card-6', '可再生能源发电量统计', 9876.54, 'kWh/日', -5.2, 'fas fa-solar-panel');
            
            // 不同数值大小测试
            createDataCard('test-card-7', '小数值', 0.123, 'MW', 0.1, 'fas fa-chart-line');
            createDataCard('test-card-8', '大数值', 1234567.89, 'Wh', 25.6, 'fas fa-chart-bar');
            createDataCard('test-card-9', '零值', 0, 'kW', 0, 'fas fa-power-off');
        });
    </script>
</body>
</html>
