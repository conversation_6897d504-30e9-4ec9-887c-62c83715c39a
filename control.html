<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>控制策略 - 智慧能源管理平台</title>
    
    <!-- 外部CSS库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    
    <style>
        .control-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .control-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .control-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #1890ff, #40a9ff);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin: 0 auto 16px;
        }
        
        .strategy-list {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 24px;
        }
        
        .strategy-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .strategy-item:hover {
            background: #f8f9fa;
        }
        
        .strategy-info {
            display: flex;
            align-items: center;
            gap: 16px;
            flex: 1;
        }
        
        .strategy-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: #1890ff;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }
        
        .strategy-details {
            flex: 1;
        }
        
        .strategy-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }
        
        .strategy-desc {
            font-size: 14px;
            color: #8c8c8c;
        }
        
        .strategy-status {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .status-switch {
            position: relative;
            width: 44px;
            height: 24px;
            background: #d9d9d9;
            border-radius: 12px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .status-switch.active {
            background: #52c41a;
        }
        
        .status-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s;
        }
        
        .status-switch.active::after {
            transform: translateX(20px);
        }
        
        .schedule-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .schedule-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .schedule-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }
        
        .schedule-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .time-slots {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 4px;
            margin: 16px 0;
        }
        
        .time-slot {
            height: 20px;
            border-radius: 2px;
            background: #f0f0f0;
            position: relative;
            cursor: pointer;
        }
        
        .time-slot.active {
            background: #1890ff;
        }
        
        .time-slot.peak {
            background: #f5222d;
        }
        
        .time-slot.valley {
            background: #52c41a;
        }
        
        .efficiency-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .metric-card {
            background: white;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
            margin: 8px 0;
        }
        
        .metric-label {
            font-size: 12px;
            color: #8c8c8c;
        }
        
        .metric-trend {
            font-size: 12px;
            margin-top: 4px;
        }
        
        .trend-positive {
            color: #52c41a;
        }
        
        .trend-negative {
            color: #f5222d;
        }
    </style>
</head>
<body>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化页面布局
            initializeLayout('控制策略', ['运营中心', '控制策略']);
            
            // 获取页面内容容器
            const pageContent = document.getElementById('page-content');
            
            // 插入页面内容
            pageContent.innerHTML = `
                <!-- 控制概览 -->
                <div class="control-overview">
                    <div class="control-card">
                        <div class="control-icon">
                            <i class="fas fa-play"></i>
                        </div>
                        <h3 style="margin: 0 0 8px 0; font-size: 16px;">运行策略</h3>
                        <div style="font-size: 24px; font-weight: bold; color: #1890ff; margin: 8px 0;">8</div>
                        <div style="font-size: 12px; color: #8c8c8c;">个策略正在执行</div>
                    </div>
                    
                    <div class="control-card">
                        <div class="control-icon" style="background: linear-gradient(135deg, #52c41a, #73d13d);">
                            <i class="fas fa-leaf"></i>
                        </div>
                        <h3 style="margin: 0 0 8px 0; font-size: 16px;">节能效果</h3>
                        <div style="font-size: 24px; font-weight: bold; color: #52c41a; margin: 8px 0;">15.2%</div>
                        <div style="font-size: 12px; color: #8c8c8c;">较上月节能</div>
                    </div>
                    
                    <div class="control-card">
                        <div class="control-icon" style="background: linear-gradient(135deg, #faad14, #ffc53d);">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <h3 style="margin: 0 0 8px 0; font-size: 16px;">成本节约</h3>
                        <div style="font-size: 24px; font-weight: bold; color: #faad14; margin: 8px 0;">¥12,580</div>
                        <div style="font-size: 12px; color: #8c8c8c;">本月节约</div>
                    </div>
                    
                    <div class="control-card">
                        <div class="control-icon" style="background: linear-gradient(135deg, #722ed1, #9254de);">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 style="margin: 0 0 8px 0; font-size: 16px;">执行次数</h3>
                        <div style="font-size: 24px; font-weight: bold; color: #722ed1; margin: 8px 0;">156</div>
                        <div style="font-size: 12px; color: #8c8c8c;">今日执行</div>
                    </div>
                </div>

                <!-- 策略管理 -->
                <div class="card mb-24">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-sliders-h" style="margin-right: 8px;"></i>
                            智能控制策略
                        </h3>
                        <button class="btn btn-primary" onclick="addStrategy()">
                            <i class="fas fa-plus"></i>
                            添加策略
                        </button>
                    </div>
                    <div class="card-body" style="padding: 0;">
                        <div class="strategy-list">
                            <div class="strategy-item">
                                <div class="strategy-info">
                                    <div class="strategy-icon">
                                        <i class="fas fa-snowflake"></i>
                                    </div>
                                    <div class="strategy-details">
                                        <div class="strategy-name">空调温度自动调节</div>
                                        <div class="strategy-desc">根据室外温度和人员密度自动调节空调温度设定值</div>
                                    </div>
                                </div>
                                <div class="strategy-status">
                                    <div class="status-switch active" onclick="toggleStrategy(this)"></div>
                                    <button class="btn" onclick="editStrategy(1)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="strategy-item">
                                <div class="strategy-info">
                                    <div class="strategy-icon" style="background: #52c41a;">
                                        <i class="fas fa-lightbulb"></i>
                                    </div>
                                    <div class="strategy-details">
                                        <div class="strategy-name">智能照明控制</div>
                                        <div class="strategy-desc">基于光照强度和人员活动自动调节照明亮度</div>
                                    </div>
                                </div>
                                <div class="strategy-status">
                                    <div class="status-switch active" onclick="toggleStrategy(this)"></div>
                                    <button class="btn" onclick="editStrategy(2)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="strategy-item">
                                <div class="strategy-info">
                                    <div class="strategy-icon" style="background: #faad14;">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div class="strategy-details">
                                        <div class="strategy-name">错峰用电策略</div>
                                        <div class="strategy-desc">在电价峰时段自动降低非关键设备功率</div>
                                    </div>
                                </div>
                                <div class="strategy-status">
                                    <div class="status-switch active" onclick="toggleStrategy(this)"></div>
                                    <button class="btn" onclick="editStrategy(3)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="strategy-item">
                                <div class="strategy-info">
                                    <div class="strategy-icon" style="background: #f5222d;">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </div>
                                    <div class="strategy-details">
                                        <div class="strategy-name">负荷限制保护</div>
                                        <div class="strategy-desc">当总负荷接近上限时自动切断非关键负荷</div>
                                    </div>
                                </div>
                                <div class="strategy-status">
                                    <div class="status-switch" onclick="toggleStrategy(this)"></div>
                                    <button class="btn" onclick="editStrategy(4)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 调度计划 -->
                <div class="schedule-grid">
                    <div class="schedule-card">
                        <div class="schedule-header">
                            <div class="schedule-title">空调系统调度</div>
                            <button class="btn btn-primary" onclick="editSchedule('hvac')">编辑</button>
                        </div>
                        <div style="font-size: 12px; color: #8c8c8c; margin-bottom: 8px;">
                            工作日运行时间表
                        </div>
                        <div class="time-slots">
                            <div class="time-slot"></div>
                            <div class="time-slot"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot"></div>
                            <div class="time-slot"></div>
                            <div class="time-slot"></div>
                            <div class="time-slot"></div>
                            <div class="time-slot"></div>
                        </div>
                        <div style="display: flex; justify-content: space-between; font-size: 10px; color: #8c8c8c;">
                            <span>00:00</span>
                            <span>06:00</span>
                            <span>12:00</span>
                            <span>18:00</span>
                            <span>24:00</span>
                        </div>
                    </div>
                    
                    <div class="schedule-card">
                        <div class="schedule-header">
                            <div class="schedule-title">照明系统调度</div>
                            <button class="btn btn-primary" onclick="editSchedule('lighting')">编辑</button>
                        </div>
                        <div style="font-size: 12px; color: #8c8c8c; margin-bottom: 8px;">
                            自动亮度调节时间表
                        </div>
                        <div class="time-slots">
                            <div class="time-slot"></div>
                            <div class="time-slot"></div>
                            <div class="time-slot"></div>
                            <div class="time-slot"></div>
                            <div class="time-slot"></div>
                            <div class="time-slot"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot active"></div>
                            <div class="time-slot"></div>
                            <div class="time-slot"></div>
                        </div>
                        <div style="display: flex; justify-content: space-between; font-size: 10px; color: #8c8c8c;">
                            <span>00:00</span>
                            <span>06:00</span>
                            <span>12:00</span>
                            <span>18:00</span>
                            <span>24:00</span>
                        </div>
                    </div>
                </div>

                <!-- 效果评估 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-bar" style="margin-right: 8px;"></i>
                            策略执行效果
                        </h3>
                        <select class="control-input">
                            <option value="today">今日</option>
                            <option value="week" selected>本周</option>
                            <option value="month">本月</option>
                        </select>
                    </div>
                    <div class="card-body">
                        <div class="efficiency-metrics">
                            <div class="metric-card">
                                <div class="metric-label">节能率</div>
                                <div class="metric-value">15.2%</div>
                                <div class="metric-trend trend-positive">
                                    <i class="fas fa-arrow-up"></i>
                                    较上周 +2.1%
                                </div>
                            </div>
                            
                            <div class="metric-card">
                                <div class="metric-label">成本节约</div>
                                <div class="metric-value">¥12,580</div>
                                <div class="metric-trend trend-positive">
                                    <i class="fas fa-arrow-up"></i>
                                    较上周 +8.5%
                                </div>
                            </div>
                            
                            <div class="metric-card">
                                <div class="metric-label">碳减排</div>
                                <div class="metric-value">2.8t</div>
                                <div class="metric-trend trend-positive">
                                    <i class="fas fa-arrow-up"></i>
                                    较上周 +12.3%
                                </div>
                            </div>
                            
                            <div class="metric-card">
                                <div class="metric-label">策略执行率</div>
                                <div class="metric-value">98.5%</div>
                                <div class="metric-trend trend-negative">
                                    <i class="fas fa-arrow-down"></i>
                                    较上周 -0.8%
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
        
        // 切换策略状态
        function toggleStrategy(element) {
            element.classList.toggle('active');
            const isActive = element.classList.contains('active');
            showNotification(`策略已${isActive ? '启用' : '禁用'}`, isActive ? 'success' : 'warning');
        }
        
        // 编辑策略
        function editStrategy(strategyId) {
            showNotification(`正在编辑策略 #${strategyId}`, 'info');
        }
        
        // 添加策略
        function addStrategy() {
            showNotification('正在添加新策略', 'info');
        }
        
        // 编辑调度计划
        function editSchedule(type) {
            const typeNames = {
                hvac: '空调系统',
                lighting: '照明系统'
            };
            showNotification(`正在编辑${typeNames[type]}调度计划`, 'info');
        }
    </script>
    
    <!-- 引入公共脚本 -->
    <script src="assets/js/common.js"></script>
</body>
</html>
