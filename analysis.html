<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用能分析 - 智慧能源管理平台</title>
    
    <!-- 外部CSS库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    
    <!-- 外部JS库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
    
    <style>
        .analysis-controls {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .control-row {
            display: flex;
            align-items: center;
            gap: 16px;
            flex-wrap: wrap;
        }
        
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .control-label {
            font-size: 12px;
            color: #8c8c8c;
            font-weight: 500;
        }
        
        .control-input {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .analysis-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 24px;
            margin-bottom: 24px;
        }
        
        .trend-analysis {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-bottom: 24px;
        }
        
        .comparison-table {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .comparison-row {
            display: grid;
            grid-template-columns: 2fr repeat(4, 1fr);
            align-items: center;
            padding: 12px 20px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .comparison-row:first-child {
            background: #fafafa;
            font-weight: 600;
        }
        
        .comparison-row:hover:not(:first-child) {
            background: #f8f9fa;
        }
        
        .trend-indicator {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
        }
        
        .trend-up {
            color: #f5222d;
        }
        
        .trend-down {
            color: #52c41a;
        }
        
        .trend-stable {
            color: #8c8c8c;
        }
        
        .efficiency-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .efficiency-score {
            font-size: 48px;
            font-weight: bold;
            color: #1890ff;
            margin: 16px 0;
        }
        
        .efficiency-level {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .level-excellent {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        
        .level-good {
            background: #fffbe6;
            color: #faad14;
            border: 1px solid #ffe58f;
        }
        
        .level-poor {
            background: #fff2f0;
            color: #f5222d;
            border: 1px solid #ffccc7;
        }
        
        .recommendation-list {
            list-style: none;
            padding: 0;
            margin: 16px 0 0 0;
        }
        
        .recommendation-item {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 6px;
            margin-bottom: 8px;
        }
        
        .recommendation-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #1890ff;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            flex-shrink: 0;
        }
        
        .peak-valley-chart {
            height: 200px;
            position: relative;
            margin: 16px 0;
        }
        
        @media (max-width: 768px) {
            .analysis-grid {
                grid-template-columns: 1fr;
            }
            
            .control-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .comparison-row {
                grid-template-columns: 1fr;
                gap: 8px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化页面布局
            initializeLayout('用能分析', ['分析中心', '用能分析']);
            
            // 获取页面内容容器
            const pageContent = document.getElementById('page-content');
            
            // 插入页面内容
            pageContent.innerHTML = `
                <!-- 分析控制面板 -->
                <div class="analysis-controls">
                    <div class="control-row">
                        <div class="control-group">
                            <label class="control-label">时间范围</label>
                            <select class="control-input" id="timeRange" onchange="updateAnalysis()">
                                <option value="today">今日</option>
                                <option value="week" selected>本周</option>
                                <option value="month">本月</option>
                                <option value="year">本年</option>
                                <option value="custom">自定义</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <label class="control-label">分析维度</label>
                            <select class="control-input" id="dimension" onchange="updateAnalysis()">
                                <option value="building" selected>按建筑</option>
                                <option value="floor">按楼层</option>
                                <option value="department">按部门</option>
                                <option value="device">按设备</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <label class="control-label">能源类型</label>
                            <select class="control-input" id="energyType" onchange="updateAnalysis()">
                                <option value="all" selected>全部</option>
                                <option value="electricity">电力</option>
                                <option value="water">水</option>
                                <option value="gas">燃气</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <label class="control-label">对比类型</label>
                            <select class="control-input" id="compareType" onchange="updateAnalysis()">
                                <option value="none">无对比</option>
                                <option value="previous" selected>环比</option>
                                <option value="lastyear">同比</option>
                            </select>
                        </div>
                        <button class="btn btn-primary" onclick="exportAnalysis()">
                            <i class="fas fa-download"></i>
                            导出报告
                        </button>
                    </div>
                </div>

                <!-- 趋势分析图表 -->
                <div class="trend-analysis">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">能耗趋势分析</h3>
                            <div class="trend-indicator trend-up">
                                <i class="fas fa-arrow-up"></i>
                                <span>较上周 +5.2%</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div style="height: 300px; position: relative;">
                                <canvas id="trendAnalysisChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">峰谷用电分析</h3>
                        </div>
                        <div class="card-body">
                            <div style="height: 300px; position: relative;">
                                <canvas id="peakValleyChart"></canvas>
                            </div>
                            <div style="margin-top: 16px; display: flex; justify-content: space-around; text-align: center;">
                                <div>
                                    <div style="font-size: 18px; font-weight: bold; color: #f5222d;">35%</div>
                                    <div style="font-size: 12px; color: #8c8c8c;">峰时用电</div>
                                </div>
                                <div>
                                    <div style="font-size: 18px; font-weight: bold; color: #1890ff;">45%</div>
                                    <div style="font-size: 12px; color: #8c8c8c;">平时用电</div>
                                </div>
                                <div>
                                    <div style="font-size: 18px; font-weight: bold; color: #52c41a;">20%</div>
                                    <div style="font-size: 12px; color: #8c8c8c;">谷时用电</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 对标分析和能效评估 -->
                <div class="analysis-grid">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">部门能耗对标分析</h3>
                            <span class="status-tag status-success">本周数据</span>
                        </div>
                        <div class="card-body" style="padding: 0;">
                            <div class="comparison-table">
                                <div class="comparison-row">
                                    <div>部门/区域</div>
                                    <div>本周用电(kWh)</div>
                                    <div>上周用电(kWh)</div>
                                    <div>环比变化</div>
                                    <div>单位面积能耗</div>
                                </div>
                                <div class="comparison-row">
                                    <div>生产车间</div>
                                    <div>8,731</div>
                                    <div>8,245</div>
                                    <div class="trend-indicator trend-up">
                                        <i class="fas fa-arrow-up"></i>
                                        <span>+5.9%</span>
                                    </div>
                                    <div>0.73 kWh/m²</div>
                                </div>
                                <div class="comparison-row">
                                    <div>A栋办公楼</div>
                                    <div>1,723</div>
                                    <div>1,856</div>
                                    <div class="trend-indicator trend-down">
                                        <i class="fas fa-arrow-down"></i>
                                        <span>-7.2%</span>
                                    </div>
                                    <div>0.20 kWh/m²</div>
                                </div>
                                <div class="comparison-row">
                                    <div>仓储中心</div>
                                    <div>1,098</div>
                                    <div>1,134</div>
                                    <div class="trend-indicator trend-down">
                                        <i class="fas fa-arrow-down"></i>
                                        <span>-3.2%</span>
                                    </div>
                                    <div>0.16 kWh/m²</div>
                                </div>
                                <div class="comparison-row">
                                    <div>B栋办公楼</div>
                                    <div>1,323</div>
                                    <div>1,298</div>
                                    <div class="trend-indicator trend-up">
                                        <i class="fas fa-arrow-up"></i>
                                        <span>+1.9%</span>
                                    </div>
                                    <div>0.19 kWh/m²</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div>
                        <!-- 能效评估 -->
                        <div class="efficiency-card mb-24">
                            <h3 style="margin: 0 0 16px 0; font-size: 16px; font-weight: 600;">
                                <i class="fas fa-leaf" style="color: #52c41a; margin-right: 8px;"></i>
                                综合能效评估
                            </h3>
                            <div class="efficiency-score">85</div>
                            <div class="efficiency-level level-excellent">优秀</div>
                            <div style="margin-top: 16px; font-size: 12px; color: #8c8c8c;">
                                较行业平均水平高出15%
                            </div>
                        </div>

                        <!-- 节能建议 -->
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-lightbulb" style="color: #faad14; margin-right: 8px;"></i>
                                    节能建议
                                </h3>
                            </div>
                            <div class="card-body">
                                <ul class="recommendation-list">
                                    <li class="recommendation-item">
                                        <div class="recommendation-icon">1</div>
                                        <div>
                                            <div style="font-weight: 500; margin-bottom: 4px;">优化空调运行策略</div>
                                            <div style="font-size: 12px; color: #8c8c8c;">
                                                建议在非工作时间降低空调设定温度，预计节能15%
                                            </div>
                                        </div>
                                    </li>
                                    <li class="recommendation-item">
                                        <div class="recommendation-icon">2</div>
                                        <div>
                                            <div style="font-weight: 500; margin-bottom: 4px;">实施错峰用电</div>
                                            <div style="font-size: 12px; color: #8c8c8c;">
                                                将部分设备运行时间调整至谷时，可降低电费成本20%
                                            </div>
                                        </div>
                                    </li>
                                    <li class="recommendation-item">
                                        <div class="recommendation-icon">3</div>
                                        <div>
                                            <div style="font-weight: 500; margin-bottom: 4px;">更换LED照明</div>
                                            <div style="font-size: 12px; color: #8c8c8c;">
                                                仓储区域照明系统升级，预计节能30%
                                            </div>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // 初始化分析页面
            initializeAnalysis();
        });

        // 初始化分析页面
        function initializeAnalysis() {
            // 创建趋势分析图表
            const trendData = {
                labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                datasets: [{
                    label: '本周用电量 (kWh)',
                    data: [1850, 1920, 1780, 1950, 1880, 1200, 980],
                    borderColor: '#1890ff',
                    backgroundColor: 'rgba(24, 144, 255, 0.1)',
                    fill: true,
                    tension: 0.4
                }, {
                    label: '上周用电量 (kWh)',
                    data: [1720, 1840, 1690, 1820, 1750, 1150, 920],
                    borderColor: '#8c8c8c',
                    backgroundColor: 'rgba(140, 140, 140, 0.1)',
                    fill: false,
                    borderDash: [5, 5],
                    tension: 0.4
                }]
            };

            createLineChart('trendAnalysisChart', trendData);

            // 创建峰谷用电分析图表
            const peakValleyData = {
                labels: Array.from({length: 24}, (_, i) => `${i.toString().padStart(2, '0')}:00`),
                datasets: [{
                    label: '用电量 (kW)',
                    data: generatePeakValleyData(),
                    backgroundColor: function(context) {
                        const hour = context.dataIndex;
                        if (hour >= 8 && hour <= 11 || hour >= 18 && hour <= 21) {
                            return '#f5222d'; // 峰时
                        } else if (hour >= 23 || hour <= 6) {
                            return '#52c41a'; // 谷时
                        } else {
                            return '#1890ff'; // 平时
                        }
                    },
                    borderWidth: 0
                }]
            };

            createBarChart('peakValleyChart', peakValleyData, {
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        title: {
                            display: true,
                            text: '用电量 (kW)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '时间'
                        }
                    }
                }
            });
        }

        // 生成峰谷用电数据
        function generatePeakValleyData() {
            const data = [];
            for (let i = 0; i < 24; i++) {
                let baseValue = 200;

                // 峰时 (8-11, 18-21)
                if ((i >= 8 && i <= 11) || (i >= 18 && i <= 21)) {
                    baseValue = 350 + Math.random() * 100;
                }
                // 谷时 (23-6)
                else if (i >= 23 || i <= 6) {
                    baseValue = 150 + Math.random() * 50;
                }
                // 平时
                else {
                    baseValue = 250 + Math.random() * 80;
                }

                data.push(baseValue);
            }
            return data;
        }

        // 更新分析数据
        function updateAnalysis() {
            const timeRange = document.getElementById('timeRange').value;
            const dimension = document.getElementById('dimension').value;
            const energyType = document.getElementById('energyType').value;
            const compareType = document.getElementById('compareType').value;

            // 这里可以根据选择的参数重新加载数据
            showNotification(`已更新分析数据: ${timeRange} - ${dimension} - ${energyType}`, 'success');

            // 重新生成图表数据
            const chart = window.trendAnalysisChart;
            if (chart) {
                // 模拟数据更新
                chart.data.datasets[0].data = Array.from({length: 7}, () => Math.random() * 500 + 1500);
                chart.update();
            }
        }

        // 导出分析报告
        function exportAnalysis() {
            showNotification('正在生成分析报告...', 'info');

            // 模拟导出过程
            setTimeout(() => {
                showNotification('分析报告已导出到下载文件夹', 'success');
            }, 2000);
        }
    </script>

    <!-- 引入公共脚本 -->
    <script src="assets/js/common.js"></script>
    <script src="assets/js/charts.js"></script>
</body>
</html>
