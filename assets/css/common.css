/* 智慧能源管理平台 - 通用样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: #f0f2f5;
    color: #333;
    line-height: 1.6;
}

/* 主要颜色变量 */
:root {
    --primary-color: #1890ff;
    --success-color: #52c41a;
    --warning-color: #faad14;
    --danger-color: #f5222d;
    --bg-color: #f0f2f5;
    --card-bg: #ffffff;
    --text-color: #333;
    --text-secondary: #8c8c8c;
    --border-color: #d9d9d9;
    --shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 布局容器 */
.layout-container {
    display: flex;
    min-height: 100vh;
}

/* 侧边栏样式 */
.sidebar {
    width: 250px;
    background: #001529;
    color: white;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #1f1f1f;
    text-align: center;
}

.sidebar-header h1 {
    font-size: 18px;
    font-weight: 600;
    color: white;
}

.sidebar-nav {
    padding: 20px 0;
}

.nav-item {
    margin-bottom: 8px;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: rgba(255,255,255,0.65);
    text-decoration: none;
    transition: all 0.3s;
}

.nav-link:hover {
    background: #1890ff;
    color: white;
}

.nav-link.active {
    background: #1890ff;
    color: white;
}

.nav-link i {
    margin-right: 12px;
    width: 16px;
    text-align: center;
}

.nav-submenu {
    padding-left: 20px;
}

.nav-submenu .nav-link {
    padding-left: 40px;
    font-size: 13px;
}

/* 主内容区域 */
.main-content {
    margin-left: 250px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* 顶部导航栏 */
.top-header {
    background: white;
    padding: 0 24px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--shadow);
    z-index: 999;
}

.breadcrumb {
    display: flex;
    align-items: center;
    font-size: 14px;
}

.breadcrumb-item {
    color: var(--text-secondary);
}

.breadcrumb-item:last-child {
    color: var(--text-color);
    font-weight: 500;
}

.breadcrumb-separator {
    margin: 0 8px;
    color: var(--text-secondary);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
}

/* 页面内容区域 */
.page-content {
    padding: 24px;
    flex: 1;
    overflow-y: auto;
}

/* 卡片样式 */
.card {
    background: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--shadow);
    margin-bottom: 24px;
}

.card-header {
    padding: 16px 24px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
}

.card-body {
    padding: 24px;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    text-decoration: none;
    transition: all 0.3s;
    gap: 8px;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #40a9ff;
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

/* 表格样式 */
.table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.table th,
.table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    background: #fafafa;
    font-weight: 600;
    color: var(--text-color);
}

/* 状态标签 */
.status-tag {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-success {
    background: #f6ffed;
    color: var(--success-color);
    border: 1px solid #b7eb8f;
}

.status-warning {
    background: #fffbe6;
    color: var(--warning-color);
    border: 1px solid #ffe58f;
}

.status-danger {
    background: #fff2f0;
    color: var(--danger-color);
    border: 1px solid #ffccc7;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s;
    }
    
    .sidebar.mobile-open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .page-content {
        padding: 16px;
    }
}

/* 工具类 */
.text-center { text-align: center; }
.text-right { text-align: right; }
.mb-16 { margin-bottom: 16px; }
.mb-24 { margin-bottom: 24px; }
.mt-16 { margin-top: 16px; }
.flex { display: flex; }
.flex-between { justify-content: space-between; }
.flex-center { justify-content: center; }
.align-center { align-items: center; }
.gap-16 { gap: 16px; }
