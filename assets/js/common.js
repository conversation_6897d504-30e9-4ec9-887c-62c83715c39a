// 智慧能源管理平台 - 通用JavaScript功能

// 导航菜单配置
const menuConfig = [
    {
        title: '首页总览',
        icon: 'fas fa-home',
        url: 'index.html',
        key: 'dashboard'
    },
    {
        title: '监测中心',
        icon: 'fas fa-chart-line',
        key: 'monitoring',
        children: [
            { title: '实时监测', url: 'monitoring.html', key: 'realtime' },
            { title: '数据可视化', url: 'visualization.html', key: 'visualization' }
        ]
    },
    {
        title: '分析中心',
        icon: 'fas fa-analytics',
        key: 'analysis',
        children: [
            { title: '用能分析', url: 'analysis.html', key: 'energy-analysis' },
            { title: 'AI预测', url: 'prediction.html', key: 'prediction' }
        ]
    },
    {
        title: '运营中心',
        icon: 'fas fa-cogs',
        key: 'operation',
        children: [
            { title: '告警管理', url: 'alerts.html', key: 'alerts' },
            { title: '控制策略', url: 'control.html', key: 'control' },
            { title: '设备管理', url: 'equipment.html', key: 'equipment' },
            { title: '光伏管理', url: 'solar.html', key: 'solar' },
            { title: '充电桩管理', url: 'charging.html', key: 'charging' },
            { title: '储能管理', url: 'storage.html', key: 'storage' }
        ]
    },
    {
        title: '报表中心',
        icon: 'fas fa-file-alt',
        url: 'reports.html',
        key: 'reports'
    },
    {
        title: '碳资产管理',
        icon: 'fas fa-leaf',
        url: 'carbon.html',
        key: 'carbon'
    },
    {
        title: '系统管理',
        icon: 'fas fa-cog',
        url: 'system.html',
        key: 'system'
    }
];

// 生成侧边栏HTML
function generateSidebar() {
    const currentPage = getCurrentPage();
    let sidebarHTML = `
        <div class="sidebar">
            <div class="sidebar-header">
                <h1><i class="fas fa-bolt"></i> 智慧能源管理平台</h1>
            </div>
            <nav class="sidebar-nav">
    `;

    menuConfig.forEach(item => {
        if (item.children) {
            sidebarHTML += `
                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="toggleSubmenu('${item.key}')">
                        <i class="${item.icon}"></i>
                        <span>${item.title}</span>
                        <i class="fas fa-chevron-down" style="margin-left: auto;"></i>
                    </a>
                    <div class="nav-submenu" id="submenu-${item.key}">
            `;
            item.children.forEach(child => {
                const isActive = currentPage === child.url ? 'active' : '';
                sidebarHTML += `
                    <a href="${child.url}" class="nav-link ${isActive}">
                        <span>${child.title}</span>
                    </a>
                `;
            });
            sidebarHTML += `</div></div>`;
        } else {
            const isActive = currentPage === item.url ? 'active' : '';
            sidebarHTML += `
                <div class="nav-item">
                    <a href="${item.url}" class="nav-link ${isActive}">
                        <i class="${item.icon}"></i>
                        <span>${item.title}</span>
                    </a>
                </div>
            `;
        }
    });

    sidebarHTML += `</nav></div>`;
    return sidebarHTML;
}

// 生成顶部导航HTML
function generateTopHeader(pageTitle, breadcrumbs = []) {
    return `
        <header class="top-header">
            <div class="breadcrumb">
                <span class="breadcrumb-item">智慧能源管理平台</span>
                ${breadcrumbs.map(item => `
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-item">${item}</span>
                `).join('')}
            </div>
            <div class="header-actions">
                <div class="user-info">
                    <div class="user-avatar">管</div>
                    <span>管理员</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
        </header>
    `;
}

// 获取当前页面文件名
function getCurrentPage() {
    const path = window.location.pathname;
    return path.substring(path.lastIndexOf('/') + 1) || 'index.html';
}

// 切换子菜单
function toggleSubmenu(key) {
    const submenu = document.getElementById(`submenu-${key}`);
    if (submenu) {
        submenu.style.display = submenu.style.display === 'none' ? 'block' : 'none';
    }
}

// 初始化页面布局
function initializeLayout(pageTitle, breadcrumbs = []) {
    // 生成侧边栏
    const sidebarHTML = generateSidebar();
    document.body.insertAdjacentHTML('afterbegin', sidebarHTML);

    // 生成主内容区域
    const mainContentHTML = `
        <div class="main-content">
            ${generateTopHeader(pageTitle, breadcrumbs)}
            <main class="page-content" id="page-content">
                <!-- 页面内容将在这里插入 -->
            </main>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', mainContentHTML);

    // 添加布局容器类
    document.body.classList.add('layout-container');
}

// 格式化数字
function formatNumber(num, decimals = 2) {
    return Number(num).toLocaleString('zh-CN', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    });
}

// 格式化日期时间
function formatDateTime(date) {
    return new Date(date).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

// 显示通知消息
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        <span>${message}</span>
        <button onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    // 添加通知样式
    if (!document.querySelector('#notification-styles')) {
        const styles = document.createElement('style');
        styles.id = 'notification-styles';
        styles.textContent = `
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 16px;
                background: white;
                border-radius: 4px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                display: flex;
                align-items: center;
                gap: 8px;
                z-index: 9999;
                animation: slideIn 0.3s ease;
            }
            .notification-success { border-left: 4px solid #52c41a; }
            .notification-error { border-left: 4px solid #f5222d; }
            .notification-info { border-left: 4px solid #1890ff; }
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(styles);
    }
    
    document.body.appendChild(notification);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 3000);
}

// 模拟数据生成器
function generateMockData() {
    return {
        // 实时数据
        realTimeData: {
            totalPower: Math.random() * 1000 + 500,
            waterUsage: Math.random() * 100 + 50,
            gasUsage: Math.random() * 50 + 20,
            temperature: Math.random() * 10 + 20,
            humidity: Math.random() * 30 + 40
        },
        
        // 历史趋势数据
        trendData: Array.from({length: 24}, (_, i) => ({
            time: `${i.toString().padStart(2, '0')}:00`,
            power: Math.random() * 200 + 300,
            water: Math.random() * 20 + 10,
            gas: Math.random() * 10 + 5
        })),
        
        // 设备状态
        deviceStatus: [
            { id: 1, name: '空调系统', status: 'normal', power: 120.5 },
            { id: 2, name: '照明系统', status: 'normal', power: 45.2 },
            { id: 3, name: '电梯系统', status: 'warning', power: 89.7 },
            { id: 4, name: '供水系统', status: 'normal', power: 32.1 }
        ]
    };
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 可以在这里添加全局初始化代码
    console.log('智慧能源管理平台已加载');
});
