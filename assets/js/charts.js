// 图表组件库 - 基于Chart.js

// 默认图表配置
const defaultChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: {
            position: 'top',
        }
    },
    scales: {
        y: {
            beginAtZero: true,
            grid: {
                color: '#f0f0f0'
            }
        },
        x: {
            grid: {
                color: '#f0f0f0'
            }
        }
    }
};

// 创建折线图
function createLineChart(canvasId, data, options = {}) {
    const ctx = document.getElementById(canvasId).getContext('2d');
    const config = {
        type: 'line',
        data: data,
        options: {
            ...defaultChartOptions,
            ...options
        }
    };
    return new Chart(ctx, config);
}

// 创建柱状图
function createBarChart(canvasId, data, options = {}) {
    const ctx = document.getElementById(canvasId).getContext('2d');
    const config = {
        type: 'bar',
        data: data,
        options: {
            ...defaultChartOptions,
            ...options
        }
    };
    return new Chart(ctx, config);
}

// 创建饼图
function createPieChart(canvasId, data, options = {}) {
    const ctx = document.getElementById(canvasId).getContext('2d');
    const config = {
        type: 'pie',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right',
                }
            },
            ...options
        }
    };
    return new Chart(ctx, config);
}

// 创建极坐标图
function createPolarChart(canvasId, data, options = {}) {
    const ctx = document.getElementById(canvasId).getContext('2d');
    const config = {
        type: 'polarArea',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right',
                }
            },
            scales: {
                r: {
                    beginAtZero: true,
                    grid: {
                        color: '#f0f0f0'
                    }
                }
            },
            ...options
        }
    };
    return new Chart(ctx, config);
}

// 创建仪表盘图表
function createGaugeChart(canvasId, value, max = 100, title = '') {
    const ctx = document.getElementById(canvasId).getContext('2d');
    
    // 计算角度
    const percentage = value / max;
    const angle = percentage * Math.PI;
    
    // 清除画布
    ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
    
    const centerX = ctx.canvas.width / 2;
    const centerY = ctx.canvas.height / 2;
    const radius = Math.min(centerX, centerY) - 20;
    
    // 绘制背景弧
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, Math.PI, 2 * Math.PI);
    ctx.strokeStyle = '#e8e8e8';
    ctx.lineWidth = 20;
    ctx.stroke();
    
    // 绘制进度弧
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, Math.PI, Math.PI + angle);
    ctx.strokeStyle = percentage > 0.8 ? '#f5222d' : percentage > 0.6 ? '#faad14' : '#52c41a';
    ctx.lineWidth = 20;
    ctx.stroke();
    
    // 绘制中心文字
    ctx.fillStyle = '#333';
    ctx.font = 'bold 24px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(value.toFixed(1), centerX, centerY - 10);
    
    ctx.font = '14px Arial';
    ctx.fillText(title, centerX, centerY + 15);
}

// 创建实时数据卡片
function createDataCard(containerId, title, value, unit, trend = 0, icon = 'fas fa-chart-line') {
    const container = document.getElementById(containerId);
    const trendClass = trend > 0 ? 'trend-up' : trend < 0 ? 'trend-down' : 'trend-stable';
    const trendIcon = trend > 0 ? 'fa-arrow-up' : trend < 0 ? 'fa-arrow-down' : 'fa-minus';

    container.innerHTML = `
        <div class="data-card">
            <div class="data-card-header">
                <div class="data-card-icon">
                    <i class="${icon}"></i>
                </div>
                <div class="data-card-trend ${trendClass}">
                    <i class="fas ${trendIcon}"></i>
                    <span>${Math.abs(trend).toFixed(1)}%</span>
                </div>
            </div>
            <div class="data-card-body">
                <div class="data-card-title">${title}</div>
                <div class="data-card-value">${formatNumber(value)}</div>
                <div class="data-card-unit">${unit}</div>
            </div>
        </div>
    `;
    
    // 添加样式
    if (!document.querySelector('#data-card-styles')) {
        const styles = document.createElement('style');
        styles.id = 'data-card-styles';
        styles.textContent = `
            .data-card {
                background: white;
                border-radius: 8px;
                padding: 16px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                min-height: 140px;
                display: -webkit-box;
                display: -ms-flexbox;
                display: flex;
                -webkit-box-orient: vertical;
                -webkit-box-direction: normal;
                -ms-flex-direction: column;
                flex-direction: column;
                overflow: hidden;
                width: 100%;
                box-sizing: border-box;
            }
            .data-card-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
                flex-shrink: 0;
            }
            .data-card-icon {
                width: 36px;
                height: 36px;
                border-radius: 50%;
                background: #1890ff;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 16px;
                flex-shrink: 0;
            }
            .data-card-trend {
                display: flex;
                align-items: center;
                gap: 4px;
                font-size: 12px;
                font-weight: 500;
            }
            .trend-up { color: #52c41a; }
            .trend-down { color: #f5222d; }
            .trend-stable { color: #8c8c8c; }
            .data-card-body {
                flex: 1;
                display: flex;
                flex-direction: column;
                justify-content: center;
                min-height: 0;
                overflow: hidden;
            }
            .data-card-value {
                font-size: 24px;
                font-weight: bold;
                color: #333;
                line-height: 1.1;
                margin-bottom: 2px;
            }
            .data-card-unit {
                font-size: 13px;
                color: #8c8c8c;
                margin-top: 2px;
                line-height: 1.2;
            }
            .data-card-title {
                font-size: 13px;
                color: #666;
                margin-bottom: 8px;
                line-height: 1.2;
                font-weight: 500;
                order: -1;
            }
        `;
        document.head.appendChild(styles);
    }
}

// 创建状态指示器
function createStatusIndicator(containerId, items) {
    const container = document.getElementById(containerId);
    let html = '<div class="status-indicators">';
    
    items.forEach(item => {
        const statusClass = item.status === 'normal' ? 'status-normal' : 
                           item.status === 'warning' ? 'status-warning' : 'status-error';
        html += `
            <div class="status-item">
                <div class="status-dot ${statusClass}"></div>
                <div class="status-info">
                    <div class="status-name">${item.name}</div>
                    <div class="status-value">${item.value}</div>
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    container.innerHTML = html;
    
    // 添加样式
    if (!document.querySelector('#status-indicator-styles')) {
        const styles = document.createElement('style');
        styles.id = 'status-indicator-styles';
        styles.textContent = `
            .status-indicators {
                display: flex;
                flex-direction: column;
                gap: 12px;
            }
            .status-item {
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 8px 0;
            }
            .status-dot {
                width: 12px;
                height: 12px;
                border-radius: 50%;
                flex-shrink: 0;
            }
            .status-normal { background: #52c41a; }
            .status-warning { background: #faad14; }
            .status-error { background: #f5222d; }
            .status-info {
                flex: 1;
            }
            .status-name {
                font-size: 14px;
                color: #333;
                font-weight: 500;
            }
            .status-value {
                font-size: 12px;
                color: #8c8c8c;
                margin-top: 2px;
            }
        `;
        document.head.appendChild(styles);
    }
}

// 更新图表数据
function updateChartData(chart, newData) {
    chart.data = newData;
    chart.update('none'); // 无动画更新
}

// 生成随机颜色
function generateColors(count) {
    const colors = [
        '#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1',
        '#13c2c2', '#eb2f96', '#fa8c16', '#a0d911', '#2f54eb'
    ];
    
    const result = [];
    for (let i = 0; i < count; i++) {
        result.push(colors[i % colors.length]);
    }
    return result;
}

// 创建迷你图表
function createMiniChart(canvasId, data, type = 'line') {
    const ctx = document.getElementById(canvasId).getContext('2d');
    const config = {
        type: type,
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    display: false
                },
                y: {
                    display: false
                }
            },
            elements: {
                point: {
                    radius: 0
                }
            }
        }
    };
    return new Chart(ctx, config);
}
