<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报表中心 - 智慧能源管理平台</title>
    
    <!-- 外部CSS库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    
    <style>
        .report-templates {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .template-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
        }
        
        .template-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        
        .template-icon {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            background: linear-gradient(135deg, #1890ff, #40a9ff);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin-bottom: 16px;
        }
        
        .template-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
        }
        
        .template-desc {
            font-size: 14px;
            color: #8c8c8c;
            margin-bottom: 16px;
            line-height: 1.5;
        }
        
        .template-actions {
            display: flex;
            gap: 8px;
        }
        
        .custom-report {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 24px;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .form-label {
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }
        
        .form-input {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .report-preview {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .recent-reports {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .report-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 20px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .report-item:hover {
            background: #f8f9fa;
        }
        
        .report-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .report-icon {
            width: 32px;
            height: 32px;
            border-radius: 4px;
            background: #1890ff;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }
        
        .report-details {
            display: flex;
            flex-direction: column;
        }
        
        .report-name {
            font-weight: 500;
            color: #333;
        }
        
        .report-meta {
            font-size: 12px;
            color: #8c8c8c;
            margin-top: 2px;
        }
        
        .report-actions {
            display: flex;
            gap: 8px;
        }
        
        .action-icon {
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 4px;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .action-icon:hover {
            background: #e6f7ff;
            color: #1890ff;
        }
    </style>
</head>
<body>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化页面布局
            initializeLayout('报表中心', ['报表中心']);
            
            // 获取页面内容容器
            const pageContent = document.getElementById('page-content');
            
            // 插入页面内容
            pageContent.innerHTML = `
                <!-- 报表模板 -->
                <div class="card mb-24">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-file-alt" style="margin-right: 8px;"></i>
                            报表模板
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="report-templates">
                            <div class="template-card" onclick="generateReport('daily')">
                                <div class="template-icon">
                                    <i class="fas fa-calendar-day"></i>
                                </div>
                                <div class="template-title">日报表</div>
                                <div class="template-desc">包含当日能耗统计、设备运行状态、异常告警等信息</div>
                                <div class="template-actions">
                                    <button class="btn btn-primary">生成报表</button>
                                </div>
                            </div>
                            
                            <div class="template-card" onclick="generateReport('weekly')">
                                <div class="template-icon" style="background: linear-gradient(135deg, #52c41a, #73d13d);">
                                    <i class="fas fa-calendar-week"></i>
                                </div>
                                <div class="template-title">周报表</div>
                                <div class="template-desc">周度能耗分析、趋势对比、节能效果评估</div>
                                <div class="template-actions">
                                    <button class="btn btn-primary">生成报表</button>
                                </div>
                            </div>
                            
                            <div class="template-card" onclick="generateReport('monthly')">
                                <div class="template-icon" style="background: linear-gradient(135deg, #faad14, #ffc53d);">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <div class="template-title">月报表</div>
                                <div class="template-desc">月度能耗汇总、成本分析、能效评估报告</div>
                                <div class="template-actions">
                                    <button class="btn btn-primary">生成报表</button>
                                </div>
                            </div>
                            
                            <div class="template-card" onclick="generateReport('cost')">
                                <div class="template-icon" style="background: linear-gradient(135deg, #f5222d, #ff4d4f);">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div class="template-title">成本分析报表</div>
                                <div class="template-desc">能源成本分析、峰谷电价统计、费用优化建议</div>
                                <div class="template-actions">
                                    <button class="btn btn-primary">生成报表</button>
                                </div>
                            </div>
                            
                            <div class="template-card" onclick="generateReport('carbon')">
                                <div class="template-icon" style="background: linear-gradient(135deg, #722ed1, #9254de);">
                                    <i class="fas fa-leaf"></i>
                                </div>
                                <div class="template-title">碳排放报表</div>
                                <div class="template-desc">碳排放统计、碳足迹分析、减排效果评估</div>
                                <div class="template-actions">
                                    <button class="btn btn-primary">生成报表</button>
                                </div>
                            </div>
                            
                            <div class="template-card" onclick="showCustomReport()">
                                <div class="template-icon" style="background: linear-gradient(135deg, #13c2c2, #36cfc9);">
                                    <i class="fas fa-plus"></i>
                                </div>
                                <div class="template-title">自定义报表</div>
                                <div class="template-desc">根据需求自定义报表内容、格式和数据范围</div>
                                <div class="template-actions">
                                    <button class="btn btn-primary">创建报表</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 自定义报表配置 -->
                <div class="custom-report" id="customReportPanel" style="display: none;">
                    <h3 style="margin: 0 0 20px 0; font-size: 18px; font-weight: 600;">
                        <i class="fas fa-cog" style="margin-right: 8px; color: #1890ff;"></i>
                        自定义报表配置
                    </h3>
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">报表名称</label>
                            <input type="text" class="form-input" placeholder="请输入报表名称" id="reportName">
                        </div>
                        <div class="form-group">
                            <label class="form-label">报表类型</label>
                            <select class="form-input" id="reportType">
                                <option value="energy">能耗分析</option>
                                <option value="cost">成本分析</option>
                                <option value="efficiency">能效分析</option>
                                <option value="carbon">碳排放</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">时间范围</label>
                            <select class="form-input" id="timeRange">
                                <option value="today">今日</option>
                                <option value="week">本周</option>
                                <option value="month">本月</option>
                                <option value="quarter">本季度</option>
                                <option value="year">本年</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">数据维度</label>
                            <select class="form-input" id="dataDimension">
                                <option value="building">按建筑</option>
                                <option value="floor">按楼层</option>
                                <option value="department">按部门</option>
                                <option value="device">按设备</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">输出格式</label>
                            <select class="form-input" id="outputFormat">
                                <option value="pdf">PDF</option>
                                <option value="excel">Excel</option>
                                <option value="word">Word</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">发送方式</label>
                            <select class="form-input" id="sendMethod">
                                <option value="download">直接下载</option>
                                <option value="email">邮件发送</option>
                                <option value="schedule">定时生成</option>
                            </select>
                        </div>
                    </div>
                    
                    <div style="margin-top: 20px;">
                        <button class="btn btn-primary" onclick="previewReport()">
                            <i class="fas fa-eye"></i>
                            预览报表
                        </button>
                        <button class="btn btn-success" onclick="generateCustomReport()">
                            <i class="fas fa-download"></i>
                            生成报表
                        </button>
                        <button class="btn" onclick="hideCustomReport()" style="margin-left: 8px;">
                            取消
                        </button>
                    </div>
                    
                    <div class="report-preview" id="reportPreview" style="display: none;">
                        <h4 style="margin: 0 0 16px 0;">报表预览</h4>
                        <div style="background: white; padding: 20px; border-radius: 4px; border: 1px solid #d9d9d9;">
                            <div style="text-align: center; margin-bottom: 20px;">
                                <h3 id="previewTitle">能耗分析报表</h3>
                                <p style="color: #8c8c8c;">生成时间: ${formatDateTime(new Date())}</p>
                            </div>
                            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 16px; margin-bottom: 20px;">
                                <div style="text-align: center; padding: 16px; background: #f8f9fa; border-radius: 4px;">
                                    <div style="font-size: 24px; font-weight: bold; color: #1890ff;">1,247.8</div>
                                    <div style="font-size: 12px; color: #8c8c8c;">总用电量 (kWh)</div>
                                </div>
                                <div style="text-align: center; padding: 16px; background: #f8f9fa; border-radius: 4px;">
                                    <div style="font-size: 24px; font-weight: bold; color: #52c41a;">89.5</div>
                                    <div style="font-size: 12px; color: #8c8c8c;">总用水量 (m³)</div>
                                </div>
                                <div style="text-align: center; padding: 16px; background: #f8f9fa; border-radius: 4px;">
                                    <div style="font-size: 24px; font-weight: bold; color: #faad14;">34.7</div>
                                    <div style="font-size: 12px; color: #8c8c8c;">总用气量 (m³)</div>
                                </div>
                            </div>
                            <div style="text-align: center; color: #8c8c8c; font-size: 12px;">
                                * 这是报表预览，实际报表将包含更详细的数据和图表
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 最近生成的报表 -->
                <div class="recent-reports">
                    <div class="card-header" style="padding: 16px 20px; background: #fafafa; border-bottom: 1px solid #d9d9d9;">
                        <h3 style="margin: 0; font-size: 16px; font-weight: 600;">最近生成的报表</h3>
                    </div>
                    
                    <div class="report-item">
                        <div class="report-info">
                            <div class="report-icon">
                                <i class="fas fa-file-pdf"></i>
                            </div>
                            <div class="report-details">
                                <div class="report-name">2024年1月能耗月报</div>
                                <div class="report-meta">生成时间: 2024-01-15 09:30 | 大小: 2.3MB</div>
                            </div>
                        </div>
                        <div class="report-actions">
                            <button class="action-icon" title="下载">
                                <i class="fas fa-download"></i>
                            </button>
                            <button class="action-icon" title="预览">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="action-icon" title="分享">
                                <i class="fas fa-share"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="report-item">
                        <div class="report-info">
                            <div class="report-icon" style="background: #52c41a;">
                                <i class="fas fa-file-excel"></i>
                            </div>
                            <div class="report-details">
                                <div class="report-name">设备能效分析报表</div>
                                <div class="report-meta">生成时间: 2024-01-14 16:45 | 大小: 1.8MB</div>
                            </div>
                        </div>
                        <div class="report-actions">
                            <button class="action-icon" title="下载">
                                <i class="fas fa-download"></i>
                            </button>
                            <button class="action-icon" title="预览">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="action-icon" title="分享">
                                <i class="fas fa-share"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="report-item">
                        <div class="report-info">
                            <div class="report-icon" style="background: #faad14;">
                                <i class="fas fa-file-word"></i>
                            </div>
                            <div class="report-details">
                                <div class="report-name">碳排放评估报告</div>
                                <div class="report-meta">生成时间: 2024-01-13 11:20 | 大小: 3.1MB</div>
                            </div>
                        </div>
                        <div class="report-actions">
                            <button class="action-icon" title="下载">
                                <i class="fas fa-download"></i>
                            </button>
                            <button class="action-icon" title="预览">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="action-icon" title="分享">
                                <i class="fas fa-share"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });
        
        // 生成预定义报表
        function generateReport(type) {
            const reportTypes = {
                daily: '日报表',
                weekly: '周报表',
                monthly: '月报表',
                cost: '成本分析报表',
                carbon: '碳排放报表'
            };
            
            showNotification(`正在生成${reportTypes[type]}...`, 'info');
            
            // 模拟报表生成过程
            setTimeout(() => {
                showNotification(`${reportTypes[type]}生成完成，已开始下载`, 'success');
            }, 2000);
        }
        
        // 显示自定义报表配置
        function showCustomReport() {
            document.getElementById('customReportPanel').style.display = 'block';
        }
        
        // 隐藏自定义报表配置
        function hideCustomReport() {
            document.getElementById('customReportPanel').style.display = 'none';
            document.getElementById('reportPreview').style.display = 'none';
        }
        
        // 预览报表
        function previewReport() {
            const reportName = document.getElementById('reportName').value || '自定义报表';
            document.getElementById('previewTitle').textContent = reportName;
            document.getElementById('reportPreview').style.display = 'block';
        }
        
        // 生成自定义报表
        function generateCustomReport() {
            const reportName = document.getElementById('reportName').value || '自定义报表';
            const outputFormat = document.getElementById('outputFormat').value;
            
            showNotification(`正在生成${reportName} (${outputFormat.toUpperCase()})...`, 'info');
            
            setTimeout(() => {
                showNotification(`${reportName}生成完成，已开始下载`, 'success');
                hideCustomReport();
            }, 2000);
        }
    </script>
    
    <!-- 引入公共脚本 -->
    <script src="assets/js/common.js"></script>
</body>
</html>
