<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据可视化 - 智慧能源管理平台</title>
    
    <!-- 外部CSS库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css">
    <link rel="stylesheet" href="assets/css/common.css">
    
    <!-- 外部JS库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    
    <style>
        .visualization-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 24px;
            margin-bottom: 24px;
        }
        
        .map-container {
            height: 500px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .building-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .building-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }
        
        .building-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--primary-color);
        }
        
        .building-header {
            display: flex;
            align-items: center;
            justify-content: between;
            margin-bottom: 16px;
        }
        
        .building-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: linear-gradient(135deg, #1890ff, #40a9ff);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            margin-right: 12px;
        }
        
        .building-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-top: 16px;
        }
        
        .stat-item {
            text-align: center;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #1890ff;
        }
        
        .stat-label {
            font-size: 12px;
            color: #8c8c8c;
            margin-top: 4px;
        }
        
        .energy-flow {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 24px;
        }
        
        .flow-diagram {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 20px 0;
        }
        
        .flow-node {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
            min-width: 120px;
        }
        
        .flow-node.source {
            background: linear-gradient(135deg, #52c41a, #73d13d);
            color: white;
        }
        
        .flow-node.consumption {
            background: linear-gradient(135deg, #1890ff, #40a9ff);
            color: white;
        }
        
        .flow-arrow {
            display: flex;
            align-items: center;
            color: #8c8c8c;
            font-size: 24px;
        }
        
        .device-topology {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .topology-tree {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }
        
        .tree-level {
            display: flex;
            justify-content: center;
            gap: 20px;
        }
        
        .tree-node {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 2px solid #d9d9d9;
            min-width: 100px;
            position: relative;
        }
        
        .tree-node.active {
            border-color: #52c41a;
            background: #f6ffed;
        }
        
        .tree-node.warning {
            border-color: #faad14;
            background: #fffbe6;
        }
        
        .tree-node.error {
            border-color: #f5222d;
            background: #fff2f0;
        }
        
        .node-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .node-name {
            font-size: 12px;
            font-weight: 500;
            text-align: center;
        }
        
        .node-value {
            font-size: 10px;
            color: #8c8c8c;
            margin-top: 4px;
        }
        
        @media (max-width: 768px) {
            .visualization-grid {
                grid-template-columns: 1fr;
            }
            
            .building-overview {
                grid-template-columns: 1fr;
            }
            
            .flow-diagram {
                flex-direction: column;
                gap: 16px;
            }
            
            .flow-arrow {
                transform: rotate(90deg);
            }
            
            .tree-level {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化页面布局
            initializeLayout('数据可视化', ['监测中心', '数据可视化']);
            
            // 获取页面内容容器
            const pageContent = document.getElementById('page-content');
            
            // 插入页面内容
            pageContent.innerHTML = `
                <!-- 建筑物概览 -->
                <div class="building-overview">
                    <div class="building-card">
                        <div class="building-header">
                            <div class="building-icon">
                                <i class="fas fa-building"></i>
                            </div>
                            <div>
                                <h3 style="margin: 0; font-size: 16px;">A栋办公楼</h3>
                                <p style="margin: 4px 0 0 0; font-size: 12px; color: #8c8c8c;">总面积: 8,500 m²</p>
                            </div>
                        </div>
                        <div class="building-stats">
                            <div class="stat-item">
                                <div class="stat-value">245.8</div>
                                <div class="stat-label">用电量 (kWh)</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">32.5</div>
                                <div class="stat-label">用水量 (m³)</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">15.2</div>
                                <div class="stat-label">用气量 (m³)</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">85%</div>
                                <div class="stat-label">能效比</div>
                            </div>
                        </div>
                    </div>

                    <div class="building-card">
                        <div class="building-header">
                            <div class="building-icon">
                                <i class="fas fa-industry"></i>
                            </div>
                            <div>
                                <h3 style="margin: 0; font-size: 16px;">生产车间</h3>
                                <p style="margin: 4px 0 0 0; font-size: 12px; color: #8c8c8c;">总面积: 12,000 m²</p>
                            </div>
                        </div>
                        <div class="building-stats">
                            <div class="stat-item">
                                <div class="stat-value">1,247.3</div>
                                <div class="stat-label">用电量 (kWh)</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">89.7</div>
                                <div class="stat-label">用水量 (m³)</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">45.8</div>
                                <div class="stat-label">用气量 (m³)</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">78%</div>
                                <div class="stat-label">能效比</div>
                            </div>
                        </div>
                    </div>

                    <div class="building-card">
                        <div class="building-header">
                            <div class="building-icon">
                                <i class="fas fa-warehouse"></i>
                            </div>
                            <div>
                                <h3 style="margin: 0; font-size: 16px;">仓储中心</h3>
                                <p style="margin: 4px 0 0 0; font-size: 12px; color: #8c8c8c;">总面积: 6,800 m²</p>
                            </div>
                        </div>
                        <div class="building-stats">
                            <div class="stat-item">
                                <div class="stat-value">156.9</div>
                                <div class="stat-label">用电量 (kWh)</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">12.3</div>
                                <div class="stat-label">用水量 (m³)</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">8.5</div>
                                <div class="stat-label">用气量 (m³)</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">92%</div>
                                <div class="stat-label">能效比</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 地图和设备状态 -->
                <div class="visualization-grid">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-map-marked-alt" style="margin-right: 8px;"></i>
                                园区能源分布图
                            </h3>
                            <div>
                                <button class="btn btn-primary" onclick="toggleMapView()">
                                    <i class="fas fa-layer-group"></i>
                                    切换视图
                                </button>
                            </div>
                        </div>
                        <div class="card-body" style="padding: 0;">
                            <div id="energyMap" class="map-container"></div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">实时能耗排行</h3>
                        </div>
                        <div class="card-body">
                            <div style="height: 400px; position: relative;">
                                <canvas id="energyRankingChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 能源流向图 -->
                <div class="energy-flow">
                    <h3 style="margin: 0 0 20px 0; font-size: 16px; font-weight: 600;">
                        <i class="fas fa-project-diagram" style="margin-right: 8px; color: #1890ff;"></i>
                        能源流向分析
                    </h3>
                    <div class="flow-diagram">
                        <div class="flow-node source">
                            <i class="fas fa-plug" style="font-size: 24px; margin-bottom: 8px;"></i>
                            <div style="font-weight: 600;">电网供电</div>
                            <div style="font-size: 12px; margin-top: 4px;">1,650 kW</div>
                        </div>
                        <div class="flow-arrow">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                        <div class="flow-node">
                            <i class="fas fa-bolt" style="font-size: 24px; margin-bottom: 8px; color: #1890ff;"></i>
                            <div style="font-weight: 600;">配电系统</div>
                            <div style="font-size: 12px; margin-top: 4px;">1,620 kW</div>
                        </div>
                        <div class="flow-arrow">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                        <div class="flow-node consumption">
                            <i class="fas fa-building" style="font-size: 24px; margin-bottom: 8px;"></i>
                            <div style="font-weight: 600;">终端用电</div>
                            <div style="font-size: 12px; margin-top: 4px;">1,580 kW</div>
                        </div>
                    </div>
                    <div style="text-align: center; margin-top: 16px; padding: 12px; background: #f8f9fa; border-radius: 4px;">
                        <span style="color: #52c41a; font-weight: 600;">系统效率: 95.8%</span>
                        <span style="margin: 0 16px; color: #8c8c8c;">|</span>
                        <span style="color: #faad14; font-weight: 600;">损耗: 70 kW</span>
                    </div>
                </div>

                <!-- 设备拓扑图 -->
                <div class="device-topology">
                    <h3 style="margin: 0 0 20px 0; font-size: 16px; font-weight: 600;">
                        <i class="fas fa-sitemap" style="margin-right: 8px; color: #1890ff;"></i>
                        设备拓扑结构
                    </h3>
                    <div class="topology-tree">
                        <div class="tree-level">
                            <div class="tree-node active">
                                <div class="node-icon" style="color: #52c41a;">
                                    <i class="fas fa-server"></i>
                                </div>
                                <div class="node-name">主配电柜</div>
                                <div class="node-value">1,650 kW</div>
                            </div>
                        </div>
                        <div class="tree-level">
                            <div class="tree-node active">
                                <div class="node-icon" style="color: #1890ff;">
                                    <i class="fas fa-building"></i>
                                </div>
                                <div class="node-name">A栋配电</div>
                                <div class="node-value">245 kW</div>
                            </div>
                            <div class="tree-node active">
                                <div class="node-icon" style="color: #1890ff;">
                                    <i class="fas fa-industry"></i>
                                </div>
                                <div class="node-name">生产配电</div>
                                <div class="node-value">1,247 kW</div>
                            </div>
                            <div class="tree-node warning">
                                <div class="node-icon" style="color: #faad14;">
                                    <i class="fas fa-warehouse"></i>
                                </div>
                                <div class="node-name">仓储配电</div>
                                <div class="node-value">157 kW</div>
                            </div>
                        </div>
                        <div class="tree-level">
                            <div class="tree-node active">
                                <div class="node-icon" style="color: #52c41a;">
                                    <i class="fas fa-snowflake"></i>
                                </div>
                                <div class="node-name">空调</div>
                                <div class="node-value">120 kW</div>
                            </div>
                            <div class="tree-node active">
                                <div class="node-icon" style="color: #52c41a;">
                                    <i class="fas fa-lightbulb"></i>
                                </div>
                                <div class="node-name">照明</div>
                                <div class="node-value">45 kW</div>
                            </div>
                            <div class="tree-node warning">
                                <div class="node-icon" style="color: #faad14;">
                                    <i class="fas fa-elevator"></i>
                                </div>
                                <div class="node-name">电梯</div>
                                <div class="node-value">89 kW</div>
                            </div>
                            <div class="tree-node active">
                                <div class="node-icon" style="color: #52c41a;">
                                    <i class="fas fa-tint"></i>
                                </div>
                                <div class="node-name">供水</div>
                                <div class="node-value">32 kW</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // 初始化可视化页面
            initializeVisualization();
        });

        // 初始化可视化页面
        function initializeVisualization() {
            // 初始化地图
            initializeMap();

            // 创建能耗排行图表
            const rankingData = {
                labels: ['生产车间', 'A栋办公楼', '仓储中心', 'B栋办公楼', '食堂'],
                datasets: [{
                    label: '能耗 (kWh)',
                    data: [1247, 246, 157, 189, 98],
                    backgroundColor: [
                        '#f5222d',
                        '#faad14',
                        '#1890ff',
                        '#52c41a',
                        '#722ed1'
                    ],
                    borderWidth: 0
                }]
            };

            createBarChart('energyRankingChart', rankingData, {
                indexAxis: 'y',
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '能耗 (kWh)'
                        }
                    }
                }
            });
        }

        // 初始化地图
        function initializeMap() {
            // 创建地图实例
            const map = L.map('energyMap').setView([39.9042, 116.4074], 16);

            // 添加地图图层
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);

            // 添加建筑物标记
            const buildings = [
                {
                    name: 'A栋办公楼',
                    coords: [39.9045, 116.4070],
                    power: 245.8,
                    status: 'normal',
                    icon: 'building'
                },
                {
                    name: '生产车间',
                    coords: [39.9040, 116.4078],
                    power: 1247.3,
                    status: 'normal',
                    icon: 'industry'
                },
                {
                    name: '仓储中心',
                    coords: [39.9038, 116.4065],
                    power: 156.9,
                    status: 'warning',
                    icon: 'warehouse'
                }
            ];

            buildings.forEach(building => {
                const color = building.status === 'normal' ? '#52c41a' :
                             building.status === 'warning' ? '#faad14' : '#f5222d';

                const marker = L.circleMarker(building.coords, {
                    radius: Math.sqrt(building.power) / 3,
                    fillColor: color,
                    color: '#fff',
                    weight: 2,
                    opacity: 1,
                    fillOpacity: 0.8
                }).addTo(map);

                marker.bindPopup(`
                    <div style="text-align: center;">
                        <h4 style="margin: 0 0 8px 0;">${building.name}</h4>
                        <p style="margin: 4px 0;"><strong>实时功率:</strong> ${building.power} kW</p>
                        <p style="margin: 4px 0;"><strong>状态:</strong>
                            <span style="color: ${color};">
                                ${building.status === 'normal' ? '正常' : building.status === 'warning' ? '告警' : '故障'}
                            </span>
                        </p>
                    </div>
                `);
            });

            // 保存地图实例
            window.energyMap = map;
        }

        // 切换地图视图
        function toggleMapView() {
            // 这里可以实现不同的地图视图切换
            showNotification('地图视图已切换', 'success');
        }
    </script>

    <!-- 引入公共脚本 -->
    <script src="assets/js/common.js"></script>
    <script src="assets/js/charts.js"></script>
</body>
</html>
