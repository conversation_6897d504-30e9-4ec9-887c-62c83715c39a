<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统管理 - 智慧能源管理平台</title>
    
    <!-- 外部CSS库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    
    <style>
        .system-tabs {
            display: flex;
            background: white;
            border-radius: 8px 8px 0 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 0;
        }
        
        .tab-button {
            flex: 1;
            padding: 16px 24px;
            border: none;
            background: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #8c8c8c;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }
        
        .tab-button.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
            background: #f8f9fa;
        }
        
        .tab-content {
            background: white;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 24px;
        }
        
        .user-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .user-table th,
        .user-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .user-table th {
            background: #fafafa;
            font-weight: 600;
            color: #333;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #1890ff;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            margin-right: 8px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
        }
        
        .role-tag {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .role-admin {
            background: #fff2f0;
            color: #f5222d;
            border: 1px solid #ffccc7;
        }
        
        .role-operator {
            background: #fffbe6;
            color: #faad14;
            border: 1px solid #ffe58f;
        }
        
        .role-viewer {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        
        .config-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .config-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid #e8e8e8;
        }
        
        .config-item:last-child {
            border-bottom: none;
        }
        
        .config-label {
            font-weight: 500;
            color: #333;
        }
        
        .config-desc {
            font-size: 12px;
            color: #8c8c8c;
            margin-top: 2px;
        }
        
        .config-control {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .switch {
            position: relative;
            width: 44px;
            height: 24px;
            background: #d9d9d9;
            border-radius: 12px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .switch.active {
            background: #52c41a;
        }
        
        .switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s;
        }
        
        .switch.active::after {
            transform: translateX(20px);
        }
        
        .log-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }
        
        .log-table th,
        .log-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
        }
        
        .log-table th {
            background: #fafafa;
            font-weight: 600;
            color: #333;
        }
        
        .log-level {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: 500;
        }
        
        .level-info {
            background: #e6f7ff;
            color: #1890ff;
        }
        
        .level-warning {
            background: #fffbe6;
            color: #faad14;
        }
        
        .level-error {
            background: #fff2f0;
            color: #f5222d;
        }
        
        .system-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .status-card {
            background: white;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .status-value {
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
            margin: 8px 0;
        }
        
        .status-label {
            font-size: 12px;
            color: #8c8c8c;
        }
    </style>
</head>
<body>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化页面布局
            initializeLayout('系统管理', ['系统管理']);
            
            // 获取页面内容容器
            const pageContent = document.getElementById('page-content');
            
            // 插入页面内容
            pageContent.innerHTML = `
                <!-- 标签页导航 -->
                <div class="system-tabs">
                    <button class="tab-button active" onclick="switchTab('users')">
                        <i class="fas fa-users" style="margin-right: 8px;"></i>
                        用户管理
                    </button>
                    <button class="tab-button" onclick="switchTab('config')">
                        <i class="fas fa-cog" style="margin-right: 8px;"></i>
                        系统配置
                    </button>
                    <button class="tab-button" onclick="switchTab('logs')">
                        <i class="fas fa-file-alt" style="margin-right: 8px;"></i>
                        日志审计
                    </button>
                    <button class="tab-button" onclick="switchTab('monitor')">
                        <i class="fas fa-heartbeat" style="margin-right: 8px;"></i>
                        系统监控
                    </button>
                </div>

                <!-- 用户管理 -->
                <div class="tab-content" id="usersTab">
                    <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 20px;">
                        <h3 style="margin: 0; font-size: 18px; font-weight: 600;">用户管理</h3>
                        <button class="btn btn-primary" onclick="addUser()">
                            <i class="fas fa-plus"></i>
                            添加用户
                        </button>
                    </div>
                    
                    <table class="user-table">
                        <thead>
                            <tr>
                                <th>用户</th>
                                <th>角色</th>
                                <th>部门</th>
                                <th>最后登录</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="user-info">
                                        <div class="user-avatar">管</div>
                                        <div>
                                            <div style="font-weight: 500;">管理员</div>
                                            <div style="font-size: 12px; color: #8c8c8c;"><EMAIL></div>
                                        </div>
                                    </div>
                                </td>
                                <td><span class="role-tag role-admin">系统管理员</span></td>
                                <td>信息技术部</td>
                                <td>2024-01-15 09:30</td>
                                <td><span class="status-tag status-success">在线</span></td>
                                <td>
                                    <button class="btn" onclick="editUser(1)">编辑</button>
                                    <button class="btn" onclick="resetPassword(1)">重置密码</button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="user-info">
                                        <div class="user-avatar" style="background: #52c41a;">张</div>
                                        <div>
                                            <div style="font-weight: 500;">张工程师</div>
                                            <div style="font-size: 12px; color: #8c8c8c;"><EMAIL></div>
                                        </div>
                                    </div>
                                </td>
                                <td><span class="role-tag role-operator">操作员</span></td>
                                <td>设备管理部</td>
                                <td>2024-01-15 08:45</td>
                                <td><span class="status-tag status-success">在线</span></td>
                                <td>
                                    <button class="btn" onclick="editUser(2)">编辑</button>
                                    <button class="btn" onclick="resetPassword(2)">重置密码</button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="user-info">
                                        <div class="user-avatar" style="background: #faad14;">李</div>
                                        <div>
                                            <div style="font-weight: 500;">李主管</div>
                                            <div style="font-size: 12px; color: #8c8c8c;"><EMAIL></div>
                                        </div>
                                    </div>
                                </td>
                                <td><span class="role-tag role-viewer">查看者</span></td>
                                <td>财务部</td>
                                <td>2024-01-14 17:20</td>
                                <td><span class="status-tag status-warning">离线</span></td>
                                <td>
                                    <button class="btn" onclick="editUser(3)">编辑</button>
                                    <button class="btn" onclick="resetPassword(3)">重置密码</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 系统配置 -->
                <div class="tab-content" id="configTab" style="display: none;">
                    <h3 style="margin: 0 0 20px 0; font-size: 18px; font-weight: 600;">系统配置</h3>
                    
                    <div class="config-section">
                        <h4 style="margin: 0 0 16px 0; font-size: 16px; font-weight: 600;">数据采集设置</h4>
                        <div class="config-item">
                            <div>
                                <div class="config-label">自动数据采集</div>
                                <div class="config-desc">启用自动数据采集功能</div>
                            </div>
                            <div class="config-control">
                                <div class="switch active" onclick="toggleSwitch(this)"></div>
                            </div>
                        </div>
                        <div class="config-item">
                            <div>
                                <div class="config-label">采集间隔</div>
                                <div class="config-desc">数据采集的时间间隔</div>
                            </div>
                            <div class="config-control">
                                <select class="control-input">
                                    <option value="5">5秒</option>
                                    <option value="10" selected>10秒</option>
                                    <option value="30">30秒</option>
                                    <option value="60">1分钟</option>
                                </select>
                            </div>
                        </div>
                        <div class="config-item">
                            <div>
                                <div class="config-label">数据保留期</div>
                                <div class="config-desc">历史数据保留时间</div>
                            </div>
                            <div class="config-control">
                                <select class="control-input">
                                    <option value="30">30天</option>
                                    <option value="90">90天</option>
                                    <option value="365" selected>1年</option>
                                    <option value="1095">3年</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="config-section">
                        <h4 style="margin: 0 0 16px 0; font-size: 16px; font-weight: 600;">告警设置</h4>
                        <div class="config-item">
                            <div>
                                <div class="config-label">邮件通知</div>
                                <div class="config-desc">启用邮件告警通知</div>
                            </div>
                            <div class="config-control">
                                <div class="switch active" onclick="toggleSwitch(this)"></div>
                            </div>
                        </div>
                        <div class="config-item">
                            <div>
                                <div class="config-label">短信通知</div>
                                <div class="config-desc">启用短信告警通知</div>
                            </div>
                            <div class="config-control">
                                <div class="switch" onclick="toggleSwitch(this)"></div>
                            </div>
                        </div>
                        <div class="config-item">
                            <div>
                                <div class="config-label">告警阈值</div>
                                <div class="config-desc">功率异常告警阈值</div>
                            </div>
                            <div class="config-control">
                                <input type="number" class="control-input" value="20" style="width: 80px;">
                                <span style="font-size: 12px; color: #8c8c8c;">%</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="config-section">
                        <h4 style="margin: 0 0 16px 0; font-size: 16px; font-weight: 600;">安全设置</h4>
                        <div class="config-item">
                            <div>
                                <div class="config-label">登录验证</div>
                                <div class="config-desc">启用双因素身份验证</div>
                            </div>
                            <div class="config-control">
                                <div class="switch" onclick="toggleSwitch(this)"></div>
                            </div>
                        </div>
                        <div class="config-item">
                            <div>
                                <div class="config-label">会话超时</div>
                                <div class="config-desc">用户会话超时时间</div>
                            </div>
                            <div class="config-control">
                                <select class="control-input">
                                    <option value="30">30分钟</option>
                                    <option value="60" selected>1小时</option>
                                    <option value="120">2小时</option>
                                    <option value="480">8小时</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div style="text-align: right; margin-top: 24px;">
                        <button class="btn btn-primary" onclick="saveConfig()">
                            <i class="fas fa-save"></i>
                            保存配置
                        </button>
                    </div>
                </div>

                <!-- 日志审计 -->
                <div class="tab-content" id="logsTab" style="display: none;">
                    <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 20px;">
                        <h3 style="margin: 0; font-size: 18px; font-weight: 600;">系统日志</h3>
                        <div style="display: flex; gap: 12px;">
                            <select class="control-input">
                                <option value="all">全部级别</option>
                                <option value="info">信息</option>
                                <option value="warning">警告</option>
                                <option value="error">错误</option>
                            </select>
                            <button class="btn btn-primary" onclick="exportLogs()">
                                <i class="fas fa-download"></i>
                                导出日志
                            </button>
                        </div>
                    </div>
                    
                    <table class="log-table">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>级别</th>
                                <th>用户</th>
                                <th>操作</th>
                                <th>详情</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2024-01-15 14:30:25</td>
                                <td><span class="log-level level-info">INFO</span></td>
                                <td>管理员</td>
                                <td>用户登录</td>
                                <td>用户成功登录系统</td>
                            </tr>
                            <tr>
                                <td>2024-01-15 14:28:12</td>
                                <td><span class="log-level level-warning">WARN</span></td>
                                <td>系统</td>
                                <td>设备告警</td>
                                <td>电梯系统功率异常</td>
                            </tr>
                            <tr>
                                <td>2024-01-15 14:25:08</td>
                                <td><span class="log-level level-info">INFO</span></td>
                                <td>张工程师</td>
                                <td>数据导出</td>
                                <td>导出能耗分析报表</td>
                            </tr>
                            <tr>
                                <td>2024-01-15 14:20:45</td>
                                <td><span class="log-level level-error">ERROR</span></td>
                                <td>系统</td>
                                <td>数据采集</td>
                                <td>传感器连接失败</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 系统监控 -->
                <div class="tab-content" id="monitorTab" style="display: none;">
                    <h3 style="margin: 0 0 20px 0; font-size: 18px; font-weight: 600;">系统运行状态</h3>
                    
                    <div class="system-status">
                        <div class="status-card">
                            <div class="status-label">CPU使用率</div>
                            <div class="status-value">45.2%</div>
                            <div style="font-size: 12px; color: #52c41a;">正常</div>
                        </div>
                        <div class="status-card">
                            <div class="status-label">内存使用率</div>
                            <div class="status-value">68.7%</div>
                            <div style="font-size: 12px; color: #faad14;">较高</div>
                        </div>
                        <div class="status-card">
                            <div class="status-label">磁盘使用率</div>
                            <div class="status-value">32.1%</div>
                            <div style="font-size: 12px; color: #52c41a;">正常</div>
                        </div>
                        <div class="status-card">
                            <div class="status-label">网络延迟</div>
                            <div class="status-value">12ms</div>
                            <div style="font-size: 12px; color: #52c41a;">优秀</div>
                        </div>
                        <div class="status-card">
                            <div class="status-label">在线用户</div>
                            <div class="status-value">8</div>
                            <div style="font-size: 12px; color: #8c8c8c;">当前</div>
                        </div>
                        <div class="status-card">
                            <div class="status-label">数据库连接</div>
                            <div class="status-value">正常</div>
                            <div style="font-size: 12px; color: #52c41a;">连接正常</div>
                        </div>
                    </div>
                    
                    <div style="margin-top: 24px;">
                        <button class="btn btn-primary" onclick="refreshStatus()">
                            <i class="fas fa-sync-alt"></i>
                            刷新状态
                        </button>
                        <button class="btn" onclick="restartSystem()">
                            <i class="fas fa-redo"></i>
                            重启系统
                        </button>
                    </div>
                </div>
            `;
        });
        
        // 切换标签页
        function switchTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.style.display = 'none';
            });
            
            // 移除所有按钮的活跃状态
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 显示选中的标签页
            document.getElementById(tabName + 'Tab').style.display = 'block';
            
            // 激活对应按钮
            event.target.classList.add('active');
        }
        
        // 切换开关
        function toggleSwitch(element) {
            element.classList.toggle('active');
        }
        
        // 添加用户
        function addUser() {
            showNotification('正在添加新用户', 'info');
        }
        
        // 编辑用户
        function editUser(userId) {
            showNotification(`正在编辑用户 #${userId}`, 'info');
        }
        
        // 重置密码
        function resetPassword(userId) {
            showNotification(`正在重置用户 #${userId} 密码`, 'info');
        }
        
        // 保存配置
        function saveConfig() {
            showNotification('系统配置已保存', 'success');
        }
        
        // 导出日志
        function exportLogs() {
            showNotification('正在导出系统日志...', 'info');
        }
        
        // 刷新状态
        function refreshStatus() {
            showNotification('系统状态已刷新', 'success');
        }
        
        // 重启系统
        function restartSystem() {
            if (confirm('确定要重启系统吗？这将中断所有用户连接。')) {
                showNotification('系统正在重启...', 'warning');
            }
        }
    </script>
    
    <!-- 引入公共脚本 -->
    <script src="assets/js/common.js"></script>
</body>
</html>
