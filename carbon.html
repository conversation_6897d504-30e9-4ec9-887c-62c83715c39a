<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碳资产管理 - 智慧能源管理平台</title>
    
    <!-- 外部CSS库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    
    <!-- 外部JS库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        .carbon-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .carbon-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .carbon-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: #52c41a;
            border-radius: 8px 8px 0 0;
        }
        
        .carbon-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #52c41a, #73d13d);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin: 0 auto 16px;
        }
        
        .carbon-value {
            font-size: 28px;
            font-weight: bold;
            color: #52c41a;
            margin: 8px 0;
        }
        
        .carbon-trend {
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            margin-top: 8px;
        }
        
        .trend-positive {
            color: #52c41a;
        }
        
        .trend-negative {
            color: #f5222d;
        }
        
        .carbon-footprint {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 24px;
        }
        
        .footprint-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .footprint-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .footprint-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #52c41a;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }
        
        .footprint-details {
            display: flex;
            flex-direction: column;
        }
        
        .footprint-name {
            font-weight: 500;
            color: #333;
        }
        
        .footprint-desc {
            font-size: 12px;
            color: #8c8c8c;
        }
        
        .footprint-value {
            font-size: 16px;
            font-weight: 600;
            color: #52c41a;
        }
        
        .carbon-trading {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin-bottom: 24px;
        }
        
        .trading-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .price-display {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #1890ff, #40a9ff);
            color: white;
            border-radius: 8px;
            margin-bottom: 16px;
        }
        
        .price-value {
            font-size: 32px;
            font-weight: bold;
            margin: 8px 0;
        }
        
        .price-change {
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }
        
        .certification-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        .certification-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #52c41a;
        }
        
        .cert-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .cert-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #52c41a;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }
        
        .cert-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-valid {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        
        .status-pending {
            background: #fffbe6;
            color: #faad14;
            border: 1px solid #ffe58f;
        }
        
        .reduction-targets {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .target-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .target-info {
            flex: 1;
        }
        
        .target-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }
        
        .target-desc {
            font-size: 12px;
            color: #8c8c8c;
        }
        
        .target-progress {
            width: 200px;
            margin: 0 20px;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #52c41a, #73d13d);
            border-radius: 4px;
            transition: width 0.3s;
        }
        
        .progress-text {
            font-size: 12px;
            color: #8c8c8c;
            text-align: center;
            margin-top: 4px;
        }
        
        .target-value {
            font-size: 16px;
            font-weight: 600;
            color: #52c41a;
        }
    </style>
</head>
<body>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化页面布局
            initializeLayout('碳资产管理', ['碳资产管理']);
            
            // 获取页面内容容器
            const pageContent = document.getElementById('page-content');
            
            // 插入页面内容
            pageContent.innerHTML = `
                <!-- 碳排放概览 -->
                <div class="carbon-overview">
                    <div class="carbon-card">
                        <div class="carbon-icon">
                            <i class="fas fa-leaf"></i>
                        </div>
                        <h3 style="margin: 0 0 8px 0; font-size: 16px;">总碳排放</h3>
                        <div class="carbon-value">2,847.5</div>
                        <div style="font-size: 12px; color: #8c8c8c;">吨 CO₂e/年</div>
                        <div class="carbon-trend trend-positive">
                            <i class="fas fa-arrow-down"></i>
                            <span>较去年 -12.3%</span>
                        </div>
                    </div>
                    
                    <div class="carbon-card">
                        <div class="carbon-icon" style="background: linear-gradient(135deg, #1890ff, #40a9ff);">
                            <i class="fas fa-industry"></i>
                        </div>
                        <h3 style="margin: 0 0 8px 0; font-size: 16px;">碳强度</h3>
                        <div class="carbon-value">0.42</div>
                        <div style="font-size: 12px; color: #8c8c8c;">吨 CO₂e/万元产值</div>
                        <div class="carbon-trend trend-positive">
                            <i class="fas fa-arrow-down"></i>
                            <span>较去年 -8.7%</span>
                        </div>
                    </div>
                    
                    <div class="carbon-card">
                        <div class="carbon-icon" style="background: linear-gradient(135deg, #faad14, #ffc53d);">
                            <i class="fas fa-certificate"></i>
                        </div>
                        <h3 style="margin: 0 0 8px 0; font-size: 16px;">碳配额</h3>
                        <div class="carbon-value">3,200</div>
                        <div style="font-size: 12px; color: #8c8c8c;">吨 CO₂e</div>
                        <div class="carbon-trend trend-positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>盈余 352.5 吨</span>
                        </div>
                    </div>
                    
                    <div class="carbon-card">
                        <div class="carbon-icon" style="background: linear-gradient(135deg, #722ed1, #9254de);">
                            <i class="fas fa-coins"></i>
                        </div>
                        <h3 style="margin: 0 0 8px 0; font-size: 16px;">碳资产价值</h3>
                        <div class="carbon-value">¥18.5</div>
                        <div style="font-size: 12px; color: #8c8c8c;">万元</div>
                        <div class="carbon-trend trend-positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>较上月 +5.2%</span>
                        </div>
                    </div>
                </div>

                <!-- 碳足迹分析 -->
                <div class="carbon-footprint">
                    <h3 style="margin: 0 0 20px 0; font-size: 18px; font-weight: 600;">
                        <i class="fas fa-shoe-prints" style="margin-right: 8px; color: #52c41a;"></i>
                        碳足迹分析
                    </h3>
                    
                    <div class="footprint-item">
                        <div class="footprint-info">
                            <div class="footprint-icon">
                                <i class="fas fa-bolt"></i>
                            </div>
                            <div class="footprint-details">
                                <div class="footprint-name">电力消耗</div>
                                <div class="footprint-desc">来自电网的间接排放</div>
                            </div>
                        </div>
                        <div class="footprint-value">1,892.3 吨</div>
                    </div>
                    
                    <div class="footprint-item">
                        <div class="footprint-info">
                            <div class="footprint-icon" style="background: #faad14;">
                                <i class="fas fa-fire"></i>
                            </div>
                            <div class="footprint-details">
                                <div class="footprint-name">燃气消耗</div>
                                <div class="footprint-desc">天然气燃烧直接排放</div>
                            </div>
                        </div>
                        <div class="footprint-value">567.8 吨</div>
                    </div>
                    
                    <div class="footprint-item">
                        <div class="footprint-info">
                            <div class="footprint-icon" style="background: #1890ff;">
                                <i class="fas fa-car"></i>
                            </div>
                            <div class="footprint-details">
                                <div class="footprint-name">交通运输</div>
                                <div class="footprint-desc">公务用车及物流运输</div>
                            </div>
                        </div>
                        <div class="footprint-value">234.7 吨</div>
                    </div>
                    
                    <div class="footprint-item">
                        <div class="footprint-info">
                            <div class="footprint-icon" style="background: #722ed1;">
                                <i class="fas fa-recycle"></i>
                            </div>
                            <div class="footprint-details">
                                <div class="footprint-name">废物处理</div>
                                <div class="footprint-desc">固废处理间接排放</div>
                            </div>
                        </div>
                        <div class="footprint-value">152.7 吨</div>
                    </div>
                </div>

                <!-- 碳交易和认证 -->
                <div class="carbon-trading">
                    <div class="trading-card">
                        <h3 style="margin: 0 0 20px 0; font-size: 16px; font-weight: 600;">
                            <i class="fas fa-chart-line" style="margin-right: 8px;"></i>
                            碳交易市场
                        </h3>
                        
                        <div class="price-display">
                            <div style="font-size: 14px; opacity: 0.9;">当前碳价</div>
                            <div class="price-value">¥52.80</div>
                            <div style="font-size: 12px; opacity: 0.8;">每吨 CO₂e</div>
                            <div class="price-change">
                                <i class="fas fa-arrow-up"></i>
                                <span>+2.3% (今日)</span>
                            </div>
                        </div>
                        
                        <div style="display: flex; justify-content: space-between; margin-bottom: 16px;">
                            <div style="text-align: center;">
                                <div style="font-size: 18px; font-weight: bold; color: #52c41a;">352.5</div>
                                <div style="font-size: 12px; color: #8c8c8c;">可交易配额 (吨)</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 18px; font-weight: bold; color: #1890ff;">¥18,612</div>
                                <div style="font-size: 12px; color: #8c8c8c;">预估收益</div>
                            </div>
                        </div>
                        
                        <div style="display: flex; gap: 8px;">
                            <button class="btn btn-success" onclick="sellCarbon()">
                                <i class="fas fa-arrow-up"></i>
                                出售配额
                            </button>
                            <button class="btn btn-primary" onclick="buyCarbon()">
                                <i class="fas fa-arrow-down"></i>
                                购买配额
                            </button>
                        </div>
                    </div>
                    
                    <div class="trading-card">
                        <h3 style="margin: 0 0 20px 0; font-size: 16px; font-weight: 600;">
                            <i class="fas fa-award" style="margin-right: 8px;"></i>
                            绿色认证
                        </h3>
                        
                        <div class="certification-list">
                            <div class="certification-item">
                                <div class="cert-info">
                                    <div class="cert-icon">
                                        <i class="fas fa-check"></i>
                                    </div>
                                    <div>
                                        <div style="font-weight: 500;">ISO 14001 环境管理</div>
                                        <div style="font-size: 12px; color: #8c8c8c;">有效期至 2025-06-30</div>
                                    </div>
                                </div>
                                <div class="cert-status status-valid">有效</div>
                            </div>
                            
                            <div class="certification-item">
                                <div class="cert-info">
                                    <div class="cert-icon">
                                        <i class="fas fa-leaf"></i>
                                    </div>
                                    <div>
                                        <div style="font-weight: 500;">碳中和认证</div>
                                        <div style="font-size: 12px; color: #8c8c8c;">申请中</div>
                                    </div>
                                </div>
                                <div class="cert-status status-pending">审核中</div>
                            </div>
                            
                            <div class="certification-item">
                                <div class="cert-info">
                                    <div class="cert-icon">
                                        <i class="fas fa-star"></i>
                                    </div>
                                    <div>
                                        <div style="font-weight: 500;">绿色建筑认证</div>
                                        <div style="font-size: 12px; color: #8c8c8c;">三星级认证</div>
                                    </div>
                                </div>
                                <div class="cert-status status-valid">有效</div>
                            </div>
                        </div>
                        
                        <button class="btn btn-primary" onclick="applyForCertification()" style="width: 100%; margin-top: 16px;">
                            <i class="fas fa-plus"></i>
                            申请新认证
                        </button>
                    </div>
                </div>

                <!-- 减排目标 -->
                <div class="reduction-targets">
                    <h3 style="margin: 0 0 20px 0; font-size: 18px; font-weight: 600;">
                        <i class="fas fa-target" style="margin-right: 8px; color: #52c41a;"></i>
                        减排目标进度
                    </h3>
                    
                    <div class="target-item">
                        <div class="target-info">
                            <div class="target-name">2024年减排目标</div>
                            <div class="target-desc">相比2023年减少碳排放15%</div>
                        </div>
                        <div class="target-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 82%;"></div>
                            </div>
                            <div class="progress-text">82% 完成</div>
                        </div>
                        <div class="target-value">12.3%</div>
                    </div>
                    
                    <div class="target-item">
                        <div class="target-info">
                            <div class="target-name">碳中和目标</div>
                            <div class="target-desc">2030年实现碳中和</div>
                        </div>
                        <div class="target-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 35%;"></div>
                            </div>
                            <div class="progress-text">35% 完成</div>
                        </div>
                        <div class="target-value">6年</div>
                    </div>
                    
                    <div class="target-item">
                        <div class="target-info">
                            <div class="target-name">可再生能源占比</div>
                            <div class="target-desc">2025年可再生能源占比达到50%</div>
                        </div>
                        <div class="target-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 68%;"></div>
                            </div>
                            <div class="progress-text">68% 完成</div>
                        </div>
                        <div class="target-value">34%</div>
                    </div>
                </div>
            `;
        });
        
        // 出售碳配额
        function sellCarbon() {
            showNotification('正在准备碳配额出售订单...', 'info');
        }
        
        // 购买碳配额
        function buyCarbon() {
            showNotification('正在准备碳配额购买订单...', 'info');
        }
        
        // 申请认证
        function applyForCertification() {
            showNotification('正在准备认证申请材料...', 'info');
        }
    </script>
    
    <!-- 引入公共脚本 -->
    <script src="assets/js/common.js"></script>
</body>
</html>
